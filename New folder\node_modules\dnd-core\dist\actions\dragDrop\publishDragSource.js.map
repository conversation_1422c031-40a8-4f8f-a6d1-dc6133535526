{"version": 3, "sources": ["../../../src/actions/dragDrop/publishDragSource.ts"], "sourcesContent": ["import type { Drag<PERSON>rop<PERSON>ana<PERSON>, SentinelAction } from '../../interfaces.js'\nimport { PUBLISH_DRAG_SOURCE } from './types.js'\n\nexport function createPublishDragSource(manager: DragDropManager) {\n\treturn function publishDragSource(): SentinelAction | undefined {\n\t\tconst monitor = manager.getMonitor()\n\t\tif (monitor.isDragging()) {\n\t\t\treturn { type: PUBLISH_DRAG_SOURCE }\n\t\t}\n\t\treturn\n\t}\n}\n"], "names": ["PUBLISH_DRAG_SOURCE", "createPublishDragSource", "manager", "publishDragSource", "monitor", "getMonitor", "isDragging", "type"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,YAAY,CAAA;AAEhD,OAAO,SAASC,uBAAuB,CAACC,OAAwB,EAAE;IACjE,OAAO,SAASC,iBAAiB,GAA+B;QAC/D,MAAMC,OAAO,GAAGF,OAAO,CAACG,UAAU,EAAE;QACpC,IAAID,OAAO,CAACE,UAAU,EAAE,EAAE;YACzB,OAAO;gBAAEC,IAAI,EAAEP,mBAAmB;aAAE,CAAA;SACpC;QACD,OAAM;KACN,CAAA;CACD"}