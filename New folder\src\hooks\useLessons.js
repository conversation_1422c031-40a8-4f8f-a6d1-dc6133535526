import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { lessonService } from '../services/api'
import { queryKeys } from '../config/reactQuery'
import toast from 'react-hot-toast'

// Get lessons by section
export const useLessonsBySection = (sectionId) => {
  return useQuery({
    queryKey: queryKeys.lessons.bySection(sectionId),
    queryFn: () => lessonService.getBySection(sectionId),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!sectionId,
  })
}

// Get lesson by ID
export const useLesson = (id) => {
  return useQuery({
    queryKey: queryKeys.lessons.detail(id),
    queryFn: () => lessonService.getById(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
  })
}

// Create lesson mutation
export const useCreateLesson = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: lessonService.create,
    onSuccess: (data) => {
      // Invalidate related queries
      if (data.section_id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.lessons.bySection(data.section_id) })
      }
      if (data.course_id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.courses.curriculum(data.course_id) })
      }
      
      toast.success('Lesson created successfully!')
      return data
    },
    onError: (error) => {
      console.error('Create lesson error:', error)
      toast.error(error.message || 'Failed to create lesson')
    },
  })
}

// Update lesson mutation
export const useUpdateLesson = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, lessonData }) => lessonService.update(id, lessonData),
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.lessons.detail(variables.id) })
      
      if (data.section_id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.lessons.bySection(data.section_id) })
      }
      if (data.course_id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.courses.curriculum(data.course_id) })
      }
      
      toast.success('Lesson updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update lesson error:', error)
      toast.error(error.message || 'Failed to update lesson')
    },
  })
}

// Delete lesson mutation
export const useDeleteLesson = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: lessonService.delete,
    onSuccess: (data, lessonId) => {
      // Remove lesson from cache
      queryClient.removeQueries({ queryKey: queryKeys.lessons.detail(lessonId) })
      
      // Invalidate section and course queries
      queryClient.invalidateQueries({ queryKey: ['lessons'] })
      queryClient.invalidateQueries({ queryKey: ['courses'] })
      
      toast.success('Lesson deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete lesson error:', error)
      toast.error(error.message || 'Failed to delete lesson')
    },
  })
}

// Mark lesson complete mutation
export const useMarkLessonComplete = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: lessonService.markComplete,
    onSuccess: (data, lessonId) => {
      // Invalidate lesson and user progress
      queryClient.invalidateQueries({ queryKey: queryKeys.lessons.detail(lessonId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.statistics })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.enrollments })
      
      // Invalidate course curriculum to update progress
      queryClient.invalidateQueries({ queryKey: ['courses'] })
      
      toast.success('Lesson marked as complete!')
      return data
    },
    onError: (error) => {
      console.error('Mark lesson complete error:', error)
      toast.error(error.message || 'Failed to mark lesson as complete')
    },
  })
}

// Upload lesson file mutation
export const useUploadLessonFile = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, file }) => lessonService.uploadFile(id, file),
    onSuccess: (data, variables) => {
      // Invalidate lesson to refresh file list
      queryClient.invalidateQueries({ queryKey: queryKeys.lessons.detail(variables.id) })
      
      toast.success('File uploaded successfully!')
      return data
    },
    onError: (error) => {
      console.error('Upload lesson file error:', error)
      toast.error(error.message || 'Failed to upload file')
    },
  })
}
