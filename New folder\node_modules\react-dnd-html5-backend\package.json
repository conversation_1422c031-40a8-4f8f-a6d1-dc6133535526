{"name": "react-dnd-html5-backend", "version": "16.0.1", "description": "HTML5 backend for React DnD", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-dnd/react-dnd.git"}, "scripts": {"clean": "shx rm -rf dist/", "build_types": "tsc -b .", "build_esm": "swc -C module.type=es6 -d dist src/", "build": "run-s build_types build_esm"}, "dependencies": {"dnd-core": "^16.0.1"}, "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.2.168", "@types/jest": "^27.4.1", "npm-run-all": "^4.1.5", "shx": "^0.3.4", "typescript": "^4.6.3"}}