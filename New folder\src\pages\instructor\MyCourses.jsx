import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faPlus,
  faEdit,
  faTrash,
  faEye,
  faSearch,
  faFilter,
  faGraduationCap,
  faUsers,
  faClock
} from '@fortawesome/free-solid-svg-icons'
import toast from 'react-hot-toast'
import Header from '../../components/common/Header'
import LoadingSpinner from '../../components/common/LoadingSpinner'
import ImageWithFallback from '../../components/common/ImageWithFallback'
import { useCoursesForManagement, useDeleteCourse } from '../../hooks/useCourses'

const MyCourses = () => {
  // React Query hooks
  const {
    data: courses = [],
    isLoading: loading,
    error: coursesError
  } = useCoursesForManagement()

  const deleteCourse = useDeleteCourse()

  const [filteredCourses, setFilteredCourses] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')

  useEffect(() => {
    filterCourses()
  }, [courses, searchTerm, statusFilter, categoryFilter])

  const filterCourses = () => {
    // Ensure courses is an array before filtering
    if (!Array.isArray(courses)) {
      setFilteredCourses([])
      return
    }

    let filtered = courses

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(course =>
        course.title.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(course => {
        if (statusFilter === 'published') return course.is_published
        if (statusFilter === 'draft') return !course.is_published
        return true
      })
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(course => course.category === categoryFilter)
    }

    setFilteredCourses(filtered)
  }

  const handleDeleteCourse = async (courseId) => {
    if (!window.confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
      return
    }

    try {
      await courseService.delete(courseId)
      setCourses(courses.filter(course => course.id !== courseId))
      toast.success('Course deleted successfully')
    } catch (error) {
      toast.error('Failed to delete course')
    }
  }

  const getUniqueCategories = () => {
    const categories = [...new Set(courses.map(course => course.category).filter(Boolean))]
    return categories.sort()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              My Courses
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your courses and track student progress
            </p>
          </div>
          <Link
            to="/admin/courses/create"
            className="btn-primary flex items-center space-x-2"
          >
            <FontAwesomeIcon icon={faPlus} />
            <span>Create Course</span>
          </Link>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="card mb-6"
        >
          <div className="grid md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <FontAwesomeIcon
                icon={faSearch}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                placeholder="Search courses..."
                className="form-input pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Status Filter */}
            <select
              className="form-input"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
            </select>

            {/* Category Filter */}
            <select
              className="form-input"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <option value="all">All Categories</option>
              {getUniqueCategories().map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            {/* Results Count */}
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <FontAwesomeIcon icon={faFilter} className="mr-2" />
              <span>{filteredCourses.length} courses found</span>
            </div>
          </div>
        </motion.div>

        {/* Courses Grid */}
        {filteredCourses.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {filteredCourses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
                className="card hover:shadow-lg transition-shadow"
              >
                {/* Course Image */}
                <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 overflow-hidden">
                  <ImageWithFallback
                    src={course.thumbnail_url}
                    alt={course.title}
                    className="w-full h-full object-cover"
                    fallbackIcon={faGraduationCap}
                    fallbackText="Course Image"
                  />
                </div>

                {/* Course Info */}
                <div className="mb-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-gray-900 dark:text-white line-clamp-2">
                      {course.title}
                    </h3>
                    <span className={`badge ml-2 ${
                      course.is_published ? 'badge-success' : 'badge-warning'
                    }`}>
                      {course.is_published ? 'Published' : 'Draft'}
                    </span>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {course.short_description}
                  </p>

                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <FontAwesomeIcon icon={faUsers} />
                        <span>{course.enrollment_count || 0}</span>
                      </div>
                      {course.duration && (
                        <div className="flex items-center space-x-1">
                          <FontAwesomeIcon icon={faClock} />
                          <span>{course.duration}h</span>
                        </div>
                      )}
                    </div>
                    {course.category && (
                      <span className="badge badge-primary">
                        {course.category}
                      </span>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-2">
                    <Link
                      to={`/courses/${course.id}`}
                      className="text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20"
                      title="View Course"
                    >
                      <FontAwesomeIcon icon={faEye} />
                    </Link>
                    <Link
                      to={`/admin/courses/${course.id}/edit`}
                      className="text-green-600 hover:text-green-700 p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20"
                      title="Edit Course"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </Link>
                    <button
                      onClick={() => handleDeleteCourse(course.id)}
                      className="text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20"
                      title="Delete Course"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>

                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {course.enrollment_count || 0} students
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-center py-12"
          >
            <FontAwesomeIcon
              icon={faGraduationCap}
              className="text-6xl text-gray-400 mb-4"
            />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No courses found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {courses.length === 0
                ? "Get started by creating your first course."
                : "Try adjusting your search or filter criteria."
              }
            </p>
            {courses.length === 0 && (
              <Link
                to="/admin/courses/create"
                className="btn-primary"
              >
                Create Your First Course
              </Link>
            )}
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default MyCourses
