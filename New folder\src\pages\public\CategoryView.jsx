import React, { useState, useEffect } from 'react'
import { useParams, Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faGraduationCap, faSearch, faFilter, faPlay, faUser, faClock, faStar
} from '@fortawesome/free-solid-svg-icons'
import { toast } from 'react-hot-toast'
import Header from '../../components/common/Header'
import LoadingSpinner from '../../components/common/LoadingSpinner'
import ImageWithFallback from '../../components/common/ImageWithFallback'
import { useAuth } from '../../context/AuthContext'
import { useCategory } from '../../hooks/useCategories'
import { useCourses } from '../../hooks/useCourses'

const CategoryView = () => {
  const { slug } = useParams()
  const { user } = useAuth()

  // React Query hooks
  const {
    data: category,
    isLoading: categoryLoading,
    error: categoryError
  } = useCategory(slug)

  const {
    data: allCourses = [],
    isLoading: coursesLoading,
    error: coursesError
  } = useCourses()

  const [filteredCourses, setFilteredCourses] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState('all')
  const [languageFilter, setLanguageFilter] = useState('all')

  // Filter courses by category
  const courses = allCourses.filter(course =>
    course.category_slug === slug || course.category_id === category?.id
  )

  const loading = categoryLoading || coursesLoading

  useEffect(() => {
    filterCourses()
  }, [courses, searchTerm, levelFilter, languageFilter])

  const filterCourses = () => {
    // Ensure courses is an array before filtering
    if (!Array.isArray(courses)) {
      setFilteredCourses([])
      return
    }

    let filtered = courses

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(course =>
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.instructor_name?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Level filter
    if (levelFilter !== 'all') {
      filtered = filtered.filter(course => course.level_slug === levelFilter)
    }

    // Language filter
    if (languageFilter !== 'all') {
      filtered = filtered.filter(course => course.language_code === languageFilter)
    }

    setFilteredCourses(filtered)
  }

  const getUniqueLevels = () => {
    if (!Array.isArray(courses)) return []

    const levels = [...new Set(courses.map(course => ({
      slug: course.level_slug,
      name: course.level_name
    })).filter(level => level.slug))]
    return levels
  }

  const getUniqueLanguages = () => {
    if (!Array.isArray(courses)) return []

    const languages = [...new Set(courses.map(course => ({
      code: course.language_code,
      name: course.language_name
    })).filter(lang => lang.code))]
    return languages
  }

  const isEnrolled = (course) => {
    return course.enrolledStudents?.includes(user?.id)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  if (!category) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Category Not Found
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              The category you're looking for doesn't exist.
            </p>
            <Link to="/courses" className="btn-primary">
              Browse All Courses
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Category Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center space-x-4 mb-4">
            {category.image_url && (
              <div className="w-16 h-16 rounded-lg overflow-hidden">
                <ImageWithFallback
                  src={category.image_url}
                  alt={category.name}
                  className="w-full h-full object-cover"
                  fallbackIcon={category.icon}
                  fallbackText={category.name}
                />
              </div>
            )}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {category.name}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {courses.length} courses available
              </p>
            </div>
          </div>
          {category.description && (
            <p className="text-gray-600 dark:text-gray-400 max-w-3xl">
              {category.description}
            </p>
          )}
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="card mb-6"
        >
          <div className="grid md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <FontAwesomeIcon
                icon={faSearch}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                placeholder="Search courses..."
                className="form-input pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Level Filter */}
            <select
              className="form-input"
              value={levelFilter}
              onChange={(e) => setLevelFilter(e.target.value)}
            >
              <option value="all">All Levels</option>
              {getUniqueLevels().map(level => (
                <option key={level.slug} value={level.slug}>
                  {level.name}
                </option>
              ))}
            </select>

            {/* Language Filter */}
            <select
              className="form-input"
              value={languageFilter}
              onChange={(e) => setLanguageFilter(e.target.value)}
            >
              <option value="all">All Languages</option>
              {getUniqueLanguages().map(language => (
                <option key={language.code} value={language.code}>
                  {language.name}
                </option>
              ))}
            </select>

            {/* Results Count */}
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <FontAwesomeIcon icon={faFilter} className="mr-2" />
              <span>{filteredCourses.length} courses</span>
            </div>
          </div>
        </motion.div>

        {/* Courses Grid */}
        {filteredCourses.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {filteredCourses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
                className="card hover:shadow-lg transition-shadow"
              >
                {/* Course Image */}
                <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 overflow-hidden relative">
                  <ImageWithFallback
                    src={course.thumbnail_url}
                    alt={course.title}
                    className="w-full h-full object-cover"
                    fallbackIcon={faGraduationCap}
                    fallbackText="Course Image"
                  />
                  {isEnrolled(course) && (
                    <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs">
                      Enrolled
                    </div>
                  )}
                </div>

                {/* Course Info */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">
                    {course.title}
                  </h3>

                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                    {course.short_description}
                  </p>

                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-1">
                      <FontAwesomeIcon icon={faUser} />
                      <span>{course.instructor_name}</span>
                    </div>
                    {course.duration > 0 && (
                      <div className="flex items-center space-x-1">
                        <FontAwesomeIcon icon={faClock} />
                        <span>{course.duration}h</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {course.level_name && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                          {course.level_name}
                        </span>
                      )}
                      {course.language_name && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                          {course.language_name}
                        </span>
                      )}
                    </div>
                    {course.price > 0 && (
                      <span className="text-lg font-bold text-primary-600">
                        ${course.price}
                      </span>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <Link
                      to={`/courses/${course.id}`}
                      className="w-full btn-primary flex items-center justify-center space-x-2"
                    >
                      <FontAwesomeIcon icon={isEnrolled(course) ? faPlay : faGraduationCap} />
                      <span>{isEnrolled(course) ? 'Continue Learning' : 'View Course'}</span>
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-center py-12"
          >
            <FontAwesomeIcon icon={faSearch} className="text-4xl text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No courses found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm || levelFilter !== 'all' || languageFilter !== 'all'
                ? 'Try adjusting your search terms or filters.'
                : 'No courses available in this category yet.'}
            </p>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default CategoryView
