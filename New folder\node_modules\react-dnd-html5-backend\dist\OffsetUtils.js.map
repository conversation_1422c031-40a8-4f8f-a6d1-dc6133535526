{"version": 3, "sources": ["../src/OffsetUtils.ts"], "sourcesContent": ["import type { XYCoord } from 'dnd-core'\n\nimport { isFirefox, isSafari } from './BrowserDetector.js'\nimport { MonotonicInterpolant } from './MonotonicInterpolant.js'\n\nconst ELEMENT_NODE = 1\n\nexport function getNodeClientOffset(node: Node): XYCoord | null {\n\tconst el = node.nodeType === ELEMENT_NODE ? node : node.parentElement\n\n\tif (!el) {\n\t\treturn null\n\t}\n\n\tconst { top, left } = (el as HTMLElement).getBoundingClientRect()\n\treturn { x: left, y: top }\n}\n\nexport function getEventClientOffset(e: MouseEvent): XYCoord {\n\treturn {\n\t\tx: e.clientX,\n\t\ty: e.clientY,\n\t}\n}\n\nfunction isImageNode(node: any) {\n\treturn (\n\t\tnode.nodeName === 'IMG' &&\n\t\t(isFirefox() || !document.documentElement?.contains(node))\n\t)\n}\n\nfunction getDragPreviewSize(\n\tisImage: boolean,\n\tdragPreview: any,\n\tsourceWidth: number,\n\tsourceHeight: number,\n) {\n\tlet dragPreviewWidth = isImage ? dragPreview.width : sourceWidth\n\tlet dragPreviewHeight = isImage ? dragPreview.height : sourceHeight\n\n\t// Work around @2x coordinate discrepancies in browsers\n\tif (isSafari() && isImage) {\n\t\tdragPreviewHeight /= window.devicePixelRatio\n\t\tdragPreviewWidth /= window.devicePixelRatio\n\t}\n\treturn { dragPreviewWidth, dragPreviewHeight }\n}\n\nexport function getDragPreviewOffset(\n\tsourceNode: HTMLElement,\n\tdragPreview: HTMLElement,\n\tclientOffset: XYCoord,\n\tanchorPoint: { anchorX: number; anchorY: number },\n\toffsetPoint: { offsetX: number; offsetY: number },\n): XYCoord {\n\t// The browsers will use the image intrinsic size under different conditions.\n\t// Firefox only cares if it's an image, but WebKit also wants it to be detached.\n\tconst isImage = isImageNode(dragPreview)\n\tconst dragPreviewNode = isImage ? sourceNode : dragPreview\n\tconst dragPreviewNodeOffsetFromClient = getNodeClientOffset(\n\t\tdragPreviewNode,\n\t) as XYCoord\n\tconst offsetFromDragPreview = {\n\t\tx: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n\t\ty: clientOffset.y - dragPreviewNodeOffsetFromClient.y,\n\t}\n\tconst { offsetWidth: sourceWidth, offsetHeight: sourceHeight } = sourceNode\n\tconst { anchorX, anchorY } = anchorPoint\n\tconst { dragPreviewWidth, dragPreviewHeight } = getDragPreviewSize(\n\t\tisImage,\n\t\tdragPreview,\n\t\tsourceWidth,\n\t\tsourceHeight,\n\t)\n\n\tconst calculateYOffset = () => {\n\t\tconst interpolantY = new MonotonicInterpolant(\n\t\t\t[0, 0.5, 1],\n\t\t\t[\n\t\t\t\t// Dock to the top\n\t\t\t\toffsetFromDragPreview.y,\n\t\t\t\t// Align at the center\n\t\t\t\t(offsetFromDragPreview.y / sourceHeight) * dragPreviewHeight,\n\t\t\t\t// Dock to the bottom\n\t\t\t\toffsetFromDragPreview.y + dragPreviewHeight - sourceHeight,\n\t\t\t],\n\t\t)\n\t\tlet y = interpolantY.interpolate(anchorY)\n\t\t// Work around Safari 8 positioning bug\n\t\tif (isSafari() && isImage) {\n\t\t\t// We'll have to wait for @3x to see if this is entirely correct\n\t\t\ty += (window.devicePixelRatio - 1) * dragPreviewHeight\n\t\t}\n\t\treturn y\n\t}\n\n\tconst calculateXOffset = () => {\n\t\t// Interpolate coordinates depending on anchor point\n\t\t// If you know a simpler way to do this, let me know\n\t\tconst interpolantX = new MonotonicInterpolant(\n\t\t\t[0, 0.5, 1],\n\t\t\t[\n\t\t\t\t// Dock to the left\n\t\t\t\toffsetFromDragPreview.x,\n\t\t\t\t// Align at the center\n\t\t\t\t(offsetFromDragPreview.x / sourceWidth) * dragPreviewWidth,\n\t\t\t\t// Dock to the right\n\t\t\t\toffsetFromDragPreview.x + dragPreviewWidth - sourceWidth,\n\t\t\t],\n\t\t)\n\t\treturn interpolantX.interpolate(anchorX)\n\t}\n\n\t// Force offsets if specified in the options.\n\tconst { offsetX, offsetY } = offsetPoint\n\tconst isManualOffsetX = offsetX === 0 || offsetX\n\tconst isManualOffsetY = offsetY === 0 || offsetY\n\treturn {\n\t\tx: isManualOffsetX ? offsetX : calculateXOffset(),\n\t\ty: isManualOffsetY ? offsetY : calculateYOffset(),\n\t}\n}\n"], "names": ["isFirefox", "<PERSON><PERSON><PERSON><PERSON>", "MonotonicInterpolant", "ELEMENT_NODE", "getNodeClientOffset", "node", "el", "nodeType", "parentElement", "top", "left", "getBoundingClientRect", "x", "y", "getEventClientOffset", "e", "clientX", "clientY", "isImageNode", "document", "nodeName", "documentElement", "contains", "getDragPreviewSize", "isImage", "dragPreview", "sourceWidth", "sourceHeight", "dragPreviewWidth", "width", "dragPreviewHeight", "height", "window", "devicePixelRatio", "getDragPreviewOffset", "sourceNode", "clientOffset", "anchorPoint", "offsetPoint", "dragPreviewNode", "dragPreviewNodeOffsetFromClient", "offsetFromDragPreview", "offsetWidth", "offsetHeight", "anchorX", "anchorY", "calculateYOffset", "interpolantY", "interpolate", "calculateXOffset", "interpolantX", "offsetX", "offsetY", "isManualOffsetX", "isManualOffsetY"], "mappings": "AAEA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,sBAAsB,CAAA;AAC1D,SAASC,oBAAoB,QAAQ,2BAA2B,CAAA;AAEhE,MAAMC,YAAY,GAAG,CAAC;AAEtB,OAAO,SAASC,mBAAmB,CAACC,IAAU,EAAkB;IAC/D,MAAMC,EAAE,GAAGD,IAAI,CAACE,QAAQ,KAAKJ,YAAY,GAAGE,IAAI,GAAGA,IAAI,CAACG,aAAa;IAErE,IAAI,CAACF,EAAE,EAAE;QACR,OAAO,IAAI,CAAA;KACX;IAED,MAAM,EAAEG,GAAG,CAAA,EAAEC,IAAI,CAAA,EAAE,GAAG,AAACJ,EAAE,CAAiBK,qBAAqB,EAAE;IACjE,OAAO;QAAEC,CAAC,EAAEF,IAAI;QAAEG,CAAC,EAAEJ,GAAG;KAAE,CAAA;CAC1B;AAED,OAAO,SAASK,oBAAoB,CAACC,CAAa,EAAW;IAC5D,OAAO;QACNH,CAAC,EAAEG,CAAC,CAACC,OAAO;QACZH,CAAC,EAAEE,CAAC,CAACE,OAAO;KACZ,CAAA;CACD;AAED,SAASC,WAAW,CAACb,IAAS,EAAE;QAGbc,GAAwB;IAF1C,OACCd,IAAI,CAACe,QAAQ,KAAK,KAAK,IACvB,CAACpB,SAAS,EAAE,IAAI,EAACmB,CAAAA,GAAwB,GAAxBA,QAAQ,CAACE,eAAe,cAAxBF,GAAwB,WAAU,GAAlCA,KAAAA,CAAkC,GAAlCA,GAAwB,CAAEG,QAAQ,CAACjB,IAAI,CAAC,CAAA,CAAC,CAC1D;CACD;AAED,SAASkB,kBAAkB,CAC1BC,OAAgB,EAChBC,WAAgB,EAChBC,WAAmB,EACnBC,YAAoB,EACnB;IACD,IAAIC,gBAAgB,GAAGJ,OAAO,GAAGC,WAAW,CAACI,KAAK,GAAGH,WAAW;IAChE,IAAII,iBAAiB,GAAGN,OAAO,GAAGC,WAAW,CAACM,MAAM,GAAGJ,YAAY;IAEnE,uDAAuD;IACvD,IAAI1B,QAAQ,EAAE,IAAIuB,OAAO,EAAE;QAC1BM,iBAAiB,IAAIE,MAAM,CAACC,gBAAgB;QAC5CL,gBAAgB,IAAII,MAAM,CAACC,gBAAgB;KAC3C;IACD,OAAO;QAAEL,gBAAgB;QAAEE,iBAAiB;KAAE,CAAA;CAC9C;AAED,OAAO,SAASI,oBAAoB,CACnCC,UAAuB,EACvBV,WAAwB,EACxBW,YAAqB,EACrBC,WAAiD,EACjDC,WAAiD,EACvC;IACV,6EAA6E;IAC7E,gFAAgF;IAChF,MAAMd,OAAO,GAAGN,WAAW,CAACO,WAAW,CAAC;IACxC,MAAMc,eAAe,GAAGf,OAAO,GAAGW,UAAU,GAAGV,WAAW;IAC1D,MAAMe,+BAA+B,GAAGpC,mBAAmB,CAC1DmC,eAAe,CACf,AAAW;IACZ,MAAME,qBAAqB,GAAG;QAC7B7B,CAAC,EAAEwB,YAAY,CAACxB,CAAC,GAAG4B,+BAA+B,CAAC5B,CAAC;QACrDC,CAAC,EAAEuB,YAAY,CAACvB,CAAC,GAAG2B,+BAA+B,CAAC3B,CAAC;KACrD;IACD,MAAM,EAAE6B,WAAW,EAAEhB,WAAW,CAAA,EAAEiB,YAAY,EAAEhB,YAAY,CAAA,EAAE,GAAGQ,UAAU;IAC3E,MAAM,EAAES,OAAO,CAAA,EAAEC,OAAO,CAAA,EAAE,GAAGR,WAAW;IACxC,MAAM,EAAET,gBAAgB,CAAA,EAAEE,iBAAiB,CAAA,EAAE,GAAGP,kBAAkB,CACjEC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,YAAY,CACZ;IAED,MAAMmB,gBAAgB,GAAG,IAAM;QAC9B,MAAMC,YAAY,GAAG,IAAI7C,oBAAoB,CAC5C;AAAC,aAAC;AAAE,eAAG;AAAE,aAAC;SAAC,EACX;YACC,kBAAkB;YAClBuC,qBAAqB,CAAC5B,CAAC;YACvB,sBAAsB;YACtB,CAAC4B,qBAAqB,CAAC5B,CAAC,GAAGc,YAAY,CAAC,GAAGG,iBAAiB;YAC5D,qBAAqB;YACrBW,qBAAqB,CAAC5B,CAAC,GAAGiB,iBAAiB,GAAGH,YAAY;SAC1D,CACD;QACD,IAAId,CAAC,GAAGkC,YAAY,CAACC,WAAW,CAACH,OAAO,CAAC;QACzC,uCAAuC;QACvC,IAAI5C,QAAQ,EAAE,IAAIuB,OAAO,EAAE;YAC1B,gEAAgE;YAChEX,CAAC,IAAI,CAACmB,MAAM,CAACC,gBAAgB,GAAG,CAAC,CAAC,GAAGH,iBAAiB;SACtD;QACD,OAAOjB,CAAC,CAAA;KACR;IAED,MAAMoC,gBAAgB,GAAG,IAAM;QAC9B,oDAAoD;QACpD,oDAAoD;QACpD,MAAMC,YAAY,GAAG,IAAIhD,oBAAoB,CAC5C;AAAC,aAAC;AAAE,eAAG;AAAE,aAAC;SAAC,EACX;YACC,mBAAmB;YACnBuC,qBAAqB,CAAC7B,CAAC;YACvB,sBAAsB;YACtB,CAAC6B,qBAAqB,CAAC7B,CAAC,GAAGc,WAAW,CAAC,GAAGE,gBAAgB;YAC1D,oBAAoB;YACpBa,qBAAqB,CAAC7B,CAAC,GAAGgB,gBAAgB,GAAGF,WAAW;SACxD,CACD;QACD,OAAOwB,YAAY,CAACF,WAAW,CAACJ,OAAO,CAAC,CAAA;KACxC;IAED,6CAA6C;IAC7C,MAAM,EAAEO,OAAO,CAAA,EAAEC,OAAO,CAAA,EAAE,GAAGd,WAAW;IACxC,MAAMe,eAAe,GAAGF,OAAO,KAAK,CAAC,IAAIA,OAAO;IAChD,MAAMG,eAAe,GAAGF,OAAO,KAAK,CAAC,IAAIA,OAAO;IAChD,OAAO;QACNxC,CAAC,EAAEyC,eAAe,GAAGF,OAAO,GAAGF,gBAAgB,EAAE;QACjDpC,CAAC,EAAEyC,eAAe,GAAGF,OAAO,GAAGN,gBAAgB,EAAE;KACjD,CAAA;CACD"}