{"version": 3, "sources": ["../src/contracts.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type { DragSource, DropTarget, Identifier } from './interfaces.js'\n\nexport function validateSourceContract(source: DragSource): void {\n\tinvariant(\n\t\ttypeof source.canDrag === 'function',\n\t\t'Expected canDrag to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof source.beginDrag === 'function',\n\t\t'Expected beginDrag to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof source.endDrag === 'function',\n\t\t'Expected endDrag to be a function.',\n\t)\n}\n\nexport function validateTargetContract(target: DropTarget): void {\n\tinvariant(\n\t\ttypeof target.canDrop === 'function',\n\t\t'Expected canDrop to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof target.hover === 'function',\n\t\t'Expected hover to be a function.',\n\t)\n\tinvariant(\n\t\ttypeof target.drop === 'function',\n\t\t'Expected beginDrag to be a function.',\n\t)\n}\n\nexport function validateType(\n\ttype: Identifier | Identifier[],\n\tallowArray?: boolean,\n): void {\n\tif (allowArray && Array.isArray(type)) {\n\t\ttype.forEach((t) => validateType(t, false))\n\t\treturn\n\t}\n\n\tinvariant(\n\t\ttypeof type === 'string' || typeof type === 'symbol',\n\t\tallowArray\n\t\t\t? 'Type can only be a string, a symbol, or an array of either.'\n\t\t\t: 'Type can only be a string or a symbol.',\n\t)\n}\n"], "names": ["invariant", "validateSourceContract", "source", "canDrag", "beginDrag", "endDrag", "validateTargetContract", "target", "canDrop", "hover", "drop", "validateType", "type", "allowArray", "Array", "isArray", "for<PERSON>ach", "t"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAIhD,OAAO,SAASC,sBAAsB,CAACC,MAAkB,EAAQ;IAChEF,SAAS,CACR,OAAOE,MAAM,CAACC,OAAO,KAAK,UAAU,EACpC,oCAAoC,CACpC;IACDH,SAAS,CACR,OAAOE,MAAM,CAACE,SAAS,KAAK,UAAU,EACtC,sCAAsC,CACtC;IACDJ,SAAS,CACR,OAAOE,MAAM,CAACG,OAAO,KAAK,UAAU,EACpC,oCAAoC,CACpC;CACD;AAED,OAAO,SAASC,sBAAsB,CAACC,MAAkB,EAAQ;IAChEP,SAAS,CACR,OAAOO,MAAM,CAACC,OAAO,KAAK,UAAU,EACpC,oCAAoC,CACpC;IACDR,SAAS,CACR,OAAOO,MAAM,CAACE,KAAK,KAAK,UAAU,EAClC,kCAAkC,CAClC;IACDT,SAAS,CACR,OAAOO,MAAM,CAACG,IAAI,KAAK,UAAU,EACjC,sCAAsC,CACtC;CACD;AAED,OAAO,SAASC,YAAY,CAC3BC,IAA+B,EAC/BC,UAAoB,EACb;IACP,IAAIA,UAAU,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;QACtCA,IAAI,CAACI,OAAO,CAAC,CAACC,CAAC,GAAKN,YAAY,CAACM,CAAC,EAAE,KAAK,CAAC;QAAA,CAAC;QAC3C,OAAM;KACN;IAEDjB,SAAS,CACR,OAAOY,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,QAAQ,EACpDC,UAAU,GACP,6DAA6D,GAC7D,wCAAwC,CAC3C;CACD"}