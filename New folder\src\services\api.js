import axios from 'axios'

// Smart API URL detection with multiple fallback strategies
const getApiUrl = () => {
  // Priority 1: Environment variable (highest priority)
  if (import.meta.env.VITE_API_URL) {
    console.log('🔧 Using API URL from environment:', import.meta.env.VITE_API_URL);
    return import.meta.env.VITE_API_URL;
  }

  // Priority 2: Auto-detect based on current location
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = window.location.port;

  console.log('🌐 Auto-detecting API URL from:', { protocol, hostname, port });

  // Development environments
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    const apiUrl = `${protocol}//${hostname}:3002/api`;
    console.log('🔧 Development API URL:', apiUrl);
    return apiUrl;
  }

  // Local network IPs (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
  if (hostname.match(/^(192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.)/)) {
    const apiUrl = `${protocol}//${hostname}:3002/api`;
    console.log('🏠 Local network API URL:', apiUrl);
    return apiUrl;
  }

  // Production domains - try multiple strategies
  const strategies = [
    // Strategy 1: Same domain with /api path (most common)
    `${protocol}//${hostname}${port ? `:${port}` : ''}/api`,

    // Strategy 2: api subdomain
    `${protocol}//api.${hostname}${port ? `:${port}` : ''}`,

    // Strategy 3: backend subdomain
    `${protocol}//backend.${hostname}${port ? `:${port}` : ''}`,

    // Strategy 4: Same domain with :3002 port
    `${protocol}//${hostname}:3002/api`
  ];

  // For production, we'll use the first strategy by default
  // but log all possibilities for debugging
  const selectedUrl = strategies[0];
  console.log('🚀 Production API URL selected:', selectedUrl);
  console.log('🔄 Alternative strategies available:', strategies.slice(1));

  return selectedUrl;
};

// Test API connectivity and log results
const testApiConnectivity = async (url) => {
  try {
    const response = await fetch(`${url}/health`, {
      method: 'GET',
      timeout: 5000
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API connectivity test passed:', { url, status: data.status });
      return true;
    } else {
      console.warn('⚠️ API responded with error:', { url, status: response.status });
      return false;
    }
  } catch (error) {
    console.warn('❌ API connectivity test failed:', { url, error: error.message });
    return false;
  }
};

// Initialize API with smart configuration
const initializeApi = () => {
  const baseURL = getApiUrl();

  // Test connectivity on initialization (non-blocking)
  testApiConnectivity(baseURL).then(isConnected => {
    if (!isConnected) {
      console.warn('⚠️ Initial API connectivity test failed. The app may still work if the API becomes available later.');
    }
  });

  return axios.create({
    baseURL,
    timeout: 15000, // Increased timeout for better reliability
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    // Add retry configuration
    retry: 3,
    retryDelay: 1000,
  });
};

// Create axios instance
const api = initializeApi();

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Enhanced response interceptor with smart error handling
api.interceptors.response.use(
  (response) => {
    // Log successful requests in development
    if (import.meta.env.DEV) {
      console.log('✅ API Success:', response.config.method?.toUpperCase(), response.config.url);
    }
    return response.data;
  },
  async (error) => {
    const originalRequest = error.config;

    // Log errors for debugging
    console.error('❌ API Error:', {
      method: originalRequest?.method?.toUpperCase(),
      url: originalRequest?.url,
      status: error.response?.status,
      message: error.message
    });

    // Handle network errors (API server down, CORS issues, etc.)
    if (!error.response) {
      console.error('🌐 Network Error - API server may be down or unreachable');

      // Try to suggest solutions based on the error
      if (error.message.includes('CORS')) {
        console.error('💡 CORS Error detected. Check if the API server is running and CORS is properly configured.');
      } else if (error.message.includes('Network Error')) {
        console.error('💡 Network Error detected. Check your internet connection and API server status.');
      }

      return Promise.reject({
        message: 'Unable to connect to the server. Please check your internet connection and try again.',
        type: 'NETWORK_ERROR',
        originalError: error.message
      });
    }

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401) {
      console.warn('🔐 Unauthorized access - redirecting to login');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      return Promise.reject(error.response?.data || error.message);
    }

    // Handle 403 errors for banned accounts - preserve full error structure
    if (error.response?.status === 403 && error.response?.data?.isBanned) {
      console.warn('🚫 Account is banned');
      return Promise.reject(error);
    }

    // Handle 429 (Too Many Requests) with intelligent retry
    if (error.response?.status === 429 && !originalRequest._retry) {
      originalRequest._retry = true;

      const retryAfter = error.response.headers['retry-after'] || 2;
      const delay = parseInt(retryAfter) * 1000;

      console.warn(`⏳ Rate limited - retrying after ${retryAfter} seconds`);

      await new Promise(resolve => setTimeout(resolve, delay));

      try {
        return await api(originalRequest);
      } catch (retryError) {
        console.error('❌ Retry failed after rate limit');
        return Promise.reject(error.response?.data || error.message);
      }
    }

    // Handle 500+ server errors
    if (error.response?.status >= 500) {
      console.error('🔥 Server Error detected');
      return Promise.reject({
        message: 'Server error occurred. Please try again later.',
        type: 'SERVER_ERROR',
        status: error.response.status,
        originalError: error.response?.data || error.message
      });
    }

    return Promise.reject(error.response?.data || error.message);
  }
);

// Auth services
export const authService = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  verifyToken: (token) => api.get('/auth/verify', {
    headers: { Authorization: `Bearer ${token}` }
  }),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => api.post('/auth/reset-password', { token, password }),
  getCurrentUser: () => {
    const token = localStorage.getItem('token')
    const user = localStorage.getItem('user')
    if (token && user) {
      try {
        return JSON.parse(user)
      } catch (error) {
        console.error('Error parsing user data:', error)
        return null
      }
    }
    return null
  }
}

// Setup services
export const setupService = {
  checkSetup: () => api.get('/setup/status'),
  configureDatabase: (config) => api.post('/setup/database', config),
  createAdmin: (adminData) => api.post('/setup/admin', adminData),
  completeSetup: () => api.post('/setup/complete')
}

// Course services
export const courseService = {
  getAll: () => api.get('/courses'),
  getAllForManagement: () => api.get('/courses/manage'),
  getById: (id) => api.get(`/courses/${id}`),
  create: (courseData) => api.post('/courses', courseData),
  update: (id, courseData) => api.put(`/courses/${id}`, courseData),
  delete: (id) => api.delete(`/courses/${id}`),
  enroll: (courseId) => api.post(`/courses/${courseId}/enroll`),
  unenroll: (courseId) => api.delete(`/courses/${courseId}/enroll`),
  getCurriculum: (id) => api.get(`/courses/${id}/curriculum`)
}

// Category services
export const categoryService = {
  getAll: () => api.get('/categories'),
  getAllForAdmin: () => api.get('/categories/admin'),
  getById: (id) => api.get(`/categories/${id}`),
  create: (categoryData) => api.post('/categories', categoryData),
  update: (id, categoryData) => api.put(`/categories/${id}`, categoryData),
  delete: (id) => api.delete(`/categories/${id}`)
}

// Course Level services
export const courseLevelService = {
  getAll: () => api.get('/course-levels'),
  getAllForAdmin: () => api.get('/course-levels/admin'),
  getById: (id) => api.get(`/course-levels/${id}`),
  create: (levelData) => api.post('/course-levels', levelData),
  update: (id, levelData) => api.put(`/course-levels/${id}`, levelData),
  delete: (id) => api.delete(`/course-levels/${id}`)
}

// Course Language services
export const courseLanguageService = {
  getAll: () => api.get('/course-languages'),
  getAllForAdmin: () => api.get('/course-languages/admin'),
  getById: (id) => api.get(`/course-languages/${id}`),
  create: (languageData) => api.post('/course-languages', languageData),
  update: (id, languageData) => api.put(`/course-languages/${id}`, languageData),
  delete: (id) => api.delete(`/course-languages/${id}`)
}

// Course Class services
export const courseClassService = {
  getAll: () => api.get('/course-classes'),
  getAllForAdmin: () => api.get('/course-classes/admin'),
  getById: (id) => api.get(`/course-classes/${id}`),
  create: (classData) => api.post('/course-classes', classData),
  update: (id, classData) => api.put(`/course-classes/${id}`, classData),
  delete: (id) => api.delete(`/course-classes/${id}`)
}

// Section services
export const sectionService = {
  getByCourse: (courseId) => api.get(`/courses/${courseId}/sections`),
  create: (courseId, sectionData) => api.post(`/courses/${courseId}/sections`, sectionData),
  update: (id, sectionData) => api.put(`/sections/${id}`, sectionData),
  delete: (id) => api.delete(`/sections/${id}`)
}

// Lesson services
export const lessonService = {
  getBySection: (sectionId) => api.get(`/lessons/section/${sectionId}`),
  getById: (id) => api.get(`/lessons/${id}`),
  create: (lessonData) => api.post(`/lessons`, lessonData),
  update: (id, lessonData) => api.put(`/lessons/${id}`, lessonData),
  delete: (id) => api.delete(`/lessons/${id}`),
  markComplete: (id) => api.post(`/lessons/${id}/complete`),
  uploadFile: (id, file) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post(`/lessons/${id}/files`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}

// Quiz services
export const quizService = {
  getBySection: (sectionId) => api.get(`/quizzes/section/${sectionId}`),
  getByCourse: (courseId) => api.get(`/courses/${courseId}/quizzes`),
  getById: (id) => api.get(`/quizzes/${id}`),
  getQuiz: (id) => api.get(`/quizzes/${id}`), // Alias for getById
  getQuizForEdit: (id) => api.get(`/quizzes/${id}/edit`), // For editing with questions
  create: (quizData) => api.post(`/quizzes`, quizData),
  update: (id, quizData) => api.put(`/quizzes/${id}`, quizData),
  delete: (id) => api.delete(`/quizzes/${id}`),
  submit: (id, answers) => api.post(`/quizzes/${id}/submit`, { answers }),
  submitQuiz: (id, answers) => api.post(`/quizzes/${id}/submit`, { answers }), // Alias for submit
  getResults: (id) => api.get(`/quizzes/${id}/results`)
}

// User services
export const userService = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (userData) => api.put('/users/profile', userData),
  getAllUsers: () => api.get('/users'),
  getUserById: (id) => api.get(`/users/${id}`),
  createUser: (userData) => api.post('/users', userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  // Dashboard services
  getDashboardStatistics: () => api.get('/users/dashboard/statistics'),
  getDashboardEnrollments: () => api.get('/users/dashboard/enrollments'),
  getDashboardQuizAttempts: () => api.get('/users/dashboard/quiz-attempts'),
  getProgress: () => api.get('/users/dashboard/statistics'), // Alias for compatibility
  // Progress management
  recalculateProgress: (courseId) => api.post(`/users/recalculate-progress/${courseId}`),

  // Banned accounts management
  getBannedAccounts: () => api.get('/users/banned-accounts'),
  unbanUser: (userId) => api.put(`/users/unban/${userId}`),
  deleteBanRecord: (banId) => api.delete(`/users/ban-record/${banId}`)
}

// Settings services
export const settingsService = {
  getHomepageSettings: () => api.get('/settings/homepage'),
  updateHomepageSettings: (settings) => api.put('/settings/homepage', settings),
  getWatermarkSettings: () => api.get('/settings/watermark'),
  updateWatermarkSettings: (settings) => api.put('/settings/watermark', settings),
  getAllSettings: () => api.get('/settings/all'),
  updateNotificationSettings: (settings) => api.put('/settings/notifications', settings),
  getNotificationSettings: () => api.get('/settings/notifications'),
  getSystemSettings: () => api.get('/settings/system'),
  updateSystemSetting: (key, value) => api.put(`/settings/${key}`, { value })
}

// Quiz services - additional methods
export const examService = {
  getAttemptDetails: (attemptId) => api.get(`/quizzes/attempts/${attemptId}/details`)
}

// File services
export const fileService = {
  upload: (file, type = 'general') => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)
    return api.post('/files/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  delete: (fileId) => api.delete(`/files/${fileId}`)
}

// Course File services
export const courseFileService = {
  // Course files
  addCourseFile: (courseId, fileData) => api.post(`/courses/${courseId}/files`, fileData),
  getCourseFiles: (courseId) => api.get(`/courses/${courseId}/files`),
  deleteCourseFile: (courseId, fileId) => api.delete(`/courses/${courseId}/files/${fileId}`),

  // Lesson files
  addLessonFile: (lessonId, fileData) => api.post(`/lessons/${lessonId}/files`, fileData),
  getLessonFiles: (lessonId) => api.get(`/lessons/${lessonId}/files`),
  deleteLessonFile: (lessonId, fileId) => api.delete(`/lessons/${lessonId}/files/${fileId}`)
}



// Section services for courses
export const courseSectionService = {
  getByCourse: (courseId) => api.get(`/courses/${courseId}/sections`),
  create: (courseId, sectionData) => api.post(`/courses/${courseId}/sections`, sectionData),
  update: (id, sectionData) => api.put(`/course-sections/${id}`, sectionData),
  delete: (id) => api.delete(`/course-sections/${id}`),
  reorder: (courseId, sections) => api.put(`/courses/${courseId}/sections/reorder`, { sections })
}

// Site settings services
export const siteSettingsService = {
  getSiteSettings: () => api.get('/site-settings/site'),
  updateSiteSettings: (settings) => api.put('/site-settings/site', settings),
  getCurrencies: () => api.get('/site-settings/currencies'),
  addCurrency: (currency) => api.post('/site-settings/currencies', currency),
  deleteCurrency: (id) => api.delete(`/site-settings/currencies/${id}`),
  getPublicSettings: () => api.get('/site-settings/public')
}

// Notification services
export const notificationService = {
  getAll: () => api.get('/notifications'),
  getActive: (page) => api.get(`/notifications/active/${page}`),
  create: (notification) => api.post('/notifications', notification),
  update: (id, notification) => api.put(`/notifications/${id}`, notification),
  delete: (id) => api.delete(`/notifications/${id}`),
  toggle: (id, isActive) => api.patch(`/notifications/${id}/toggle`, { isActive }),
  markViewed: (id) => api.post(`/notifications/${id}/view`)
}

// Payment services
export const paymentService = {
  // Wallet services
  getWalletBalance: () => api.get('/payments/wallet/balance'),
  getWalletTransactions: (page = 1, limit = 20) => api.get(`/payments/wallet/transactions?page=${page}&limit=${limit}`),

  // Purchase services
  purchaseWithWallet: (courseId) => api.post('/payments/purchase/wallet', { courseId }),
  redeemCode: (code) => api.post('/payments/redeem-code', { code })
}

// Payment codes services (Admin only)
export const paymentCodeService = {
  // Get all codes with filters
  getAll: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return api.get(`/payment-codes?${queryString}`);
  },

  // Generate new codes
  generate: (codeData) => api.post('/payment-codes/generate', codeData),

  // Delete single code
  delete: (id) => api.delete(`/payment-codes/${id}`),

  // Bulk delete operations
  deleteAll: () => api.delete('/payment-codes/bulk/all'),
  deleteUsedAndExpired: () => api.delete('/payment-codes/bulk/used-expired'),

  // Settings
  getSettings: () => api.get('/payment-codes/settings'),

  // Generate QR code URL using QuickChart
  generateQRCode: (code) => {
    const baseUrl = window.location.origin;
    const redemptionUrl = `${baseUrl}/redeem/${code}`;
    const qrData = encodeURIComponent(redemptionUrl);
    return `https://quickchart.io/qr?text=${qrData}&size=200`;
  }
}

// API Configuration and Diagnostics
export const apiConfig = {
  // Get current API configuration
  getCurrentConfig: () => ({
    baseURL: api.defaults.baseURL,
    timeout: api.defaults.timeout,
    headers: api.defaults.headers
  }),

  // Reconfigure API URL (useful for dynamic switching)
  reconfigure: (newBaseURL) => {
    console.log('🔄 Reconfiguring API URL from', api.defaults.baseURL, 'to', newBaseURL);
    api.defaults.baseURL = newBaseURL;

    // Test new configuration
    testApiConnectivity(newBaseURL).then(isConnected => {
      if (isConnected) {
        console.log('✅ API reconfiguration successful');
      } else {
        console.warn('⚠️ API reconfiguration may have issues - connectivity test failed');
      }
    });
  },

  // Test all possible API URLs and return the working one
  findWorkingApiUrl: async () => {
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    const port = window.location.port;

    const urlsToTest = [
      // Current configuration
      api.defaults.baseURL,

      // Common patterns
      `${protocol}//${hostname}${port ? `:${port}` : ''}/api`,
      `${protocol}//${hostname}:3002/api`,
      `${protocol}//api.${hostname}`,
      `${protocol}//backend.${hostname}`,

      // Development fallbacks
      'http://localhost:3002/api',
      'http://127.0.0.1:3002/api',
    ];

    console.log('🔍 Testing API URLs for connectivity...');

    for (const url of urlsToTest) {
      console.log(`Testing: ${url}`);
      const isWorking = await testApiConnectivity(url);
      if (isWorking) {
        console.log(`✅ Found working API URL: ${url}`);
        return url;
      }
    }

    console.error('❌ No working API URL found');
    return null;
  },

  // Auto-fix API configuration by finding working URL
  autoFix: async () => {
    const workingUrl = await apiConfig.findWorkingApiUrl();
    if (workingUrl && workingUrl !== api.defaults.baseURL) {
      apiConfig.reconfigure(workingUrl);
      return workingUrl;
    }
    return null;
  }
};

// Diagnostic services
export const diagnosticService = {
  // Test API connectivity
  testConnection: () => testApiConnectivity(api.defaults.baseURL),

  // Get comprehensive system info
  getSystemInfo: async () => {
    try {
      const response = await api.get('/health');
      return {
        api: {
          url: api.defaults.baseURL,
          status: 'connected',
          response: response
        },
        frontend: {
          url: window.location.origin,
          protocol: window.location.protocol,
          hostname: window.location.hostname,
          port: window.location.port,
          userAgent: navigator.userAgent
        }
      };
    } catch (error) {
      return {
        api: {
          url: api.defaults.baseURL,
          status: 'error',
          error: error.message
        },
        frontend: {
          url: window.location.origin,
          protocol: window.location.protocol,
          hostname: window.location.hostname,
          port: window.location.port,
          userAgent: navigator.userAgent
        }
      };
    }
  },

  // Run comprehensive diagnostics
  runDiagnostics: async () => {
    console.log('🔍 Running comprehensive diagnostics...');

    const results = {
      timestamp: new Date().toISOString(),
      apiConfig: apiConfig.getCurrentConfig(),
      connectivity: await diagnosticService.testConnection(),
      systemInfo: await diagnosticService.getSystemInfo(),
      environment: {
        isDevelopment: import.meta.env.DEV,
        mode: import.meta.env.MODE,
        apiUrl: import.meta.env.VITE_API_URL
      }
    };

    console.log('📊 Diagnostic Results:', results);
    return results;
  }
};

export default api;
