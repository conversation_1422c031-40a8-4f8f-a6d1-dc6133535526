import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { paymentService, paymentCodeService } from '../services/api'
import { queryKeys } from '../config/reactQuery'
import toast from 'react-hot-toast'

// Get wallet balance
export const useWalletBalance = () => {
  return useQuery({
    queryKey: queryKeys.payment.wallet.balance,
    queryFn: paymentService.getWalletBalance,
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!localStorage.getItem('token'),
  })
}

// Get wallet transactions
export const useWalletTransactions = (page = 1, limit = 20) => {
  return useQuery({
    queryKey: queryKeys.payment.wallet.transactions(page, limit),
    queryFn: () => paymentService.getWalletTransactions(page, limit),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!localStorage.getItem('token'),
  })
}

// Get payment codes (admin)
export const usePaymentCodes = (params = {}) => {
  return useQuery({
    queryKey: queryKeys.payment.codes.all(params),
    queryFn: () => paymentCodeService.getAll(params),
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get payment code settings
export const usePaymentCodeSettings = () => {
  return useQuery({
    queryKey: queryKeys.payment.codes.settings,
    queryFn: paymentCodeService.getSettings,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Purchase with wallet mutation
export const usePurchaseWithWallet = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: paymentService.purchaseWithWallet,
    onSuccess: (data) => {
      // Invalidate wallet balance and transactions
      queryClient.invalidateQueries({ queryKey: queryKeys.payment.wallet.balance })
      queryClient.invalidateQueries({ queryKey: ['payment', 'wallet', 'transactions'] })
      
      // Invalidate user enrollments and course data
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.enrollments })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.statistics })
      
      toast.success('Course purchased successfully!')
      return data
    },
    onError: (error) => {
      console.error('Purchase with wallet error:', error)
      toast.error(error.message || 'Failed to purchase course')
    },
  })
}

// Redeem code mutation
export const useRedeemCode = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: paymentService.redeemCode,
    onSuccess: (data) => {
      // Invalidate wallet balance and user data
      queryClient.invalidateQueries({ queryKey: queryKeys.payment.wallet.balance })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.enrollments })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.statistics })
      
      toast.success('Code redeemed successfully!')
      return data
    },
    onError: (error) => {
      console.error('Redeem code error:', error)
      toast.error(error.message || 'Failed to redeem code')
    },
  })
}

// Generate payment codes mutation (admin)
export const useGeneratePaymentCodes = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: paymentCodeService.generate,
    onSuccess: (data) => {
      // Invalidate payment codes list
      queryClient.invalidateQueries({ queryKey: ['payment', 'codes'] })
      
      toast.success('Payment codes generated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Generate payment codes error:', error)
      toast.error(error.message || 'Failed to generate payment codes')
    },
  })
}

// Delete payment code mutation (admin)
export const useDeletePaymentCode = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: paymentCodeService.delete,
    onSuccess: (data) => {
      // Invalidate payment codes list
      queryClient.invalidateQueries({ queryKey: ['payment', 'codes'] })
      
      toast.success('Payment code deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete payment code error:', error)
      toast.error(error.message || 'Failed to delete payment code')
    },
  })
}

// Delete all payment codes mutation (admin)
export const useDeleteAllPaymentCodes = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: paymentCodeService.deleteAll,
    onSuccess: (data) => {
      // Invalidate payment codes list
      queryClient.invalidateQueries({ queryKey: ['payment', 'codes'] })
      
      toast.success('All payment codes deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete all payment codes error:', error)
      toast.error(error.message || 'Failed to delete all payment codes')
    },
  })
}

// Delete used and expired payment codes mutation (admin)
export const useDeleteUsedAndExpiredCodes = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: paymentCodeService.deleteUsedAndExpired,
    onSuccess: (data) => {
      // Invalidate payment codes list
      queryClient.invalidateQueries({ queryKey: ['payment', 'codes'] })
      
      toast.success('Used and expired codes deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete used and expired codes error:', error)
      toast.error(error.message || 'Failed to delete used and expired codes')
    },
  })
}
