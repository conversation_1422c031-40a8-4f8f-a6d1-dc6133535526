{"version": 3, "sources": ["../../src/reducers/refCount.ts"], "sourcesContent": ["import {\n\tADD_SOURCE,\n\tADD_TARGET,\n\tREMOVE_SOURCE,\n\tREMOVE_TARGET,\n} from '../actions/registry.js'\nimport type { Action } from '../interfaces.js'\n\nexport type State = number\n\nexport function reduce(state: State = 0, action: Action<any>): State {\n\tswitch (action.type) {\n\t\tcase ADD_SOURCE:\n\t\tcase ADD_TARGET:\n\t\t\treturn state + 1\n\t\tcase REMOVE_SOURCE:\n\t\tcase REMOVE_TARGET:\n\t\t\treturn state - 1\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n"], "names": ["ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "reduce", "state", "action", "type"], "mappings": "AAAA,SACCA,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,aAAa,QACP,wBAAwB,CAAA;AAK/B,OAAO,SAASC,MAAM,CAACC,KAAY,GAAG,CAAC,EAAEC,MAAmB,EAAS;IACpE,OAAQA,MAAM,CAACC,IAAI;QAClB,KAAKP,UAAU,CAAC;QAChB,KAAKC,UAAU;YACd,OAAOI,KAAK,GAAG,CAAC,CAAA;QACjB,KAAKH,aAAa,CAAC;QACnB,KAAKC,aAAa;YACjB,OAAOE,KAAK,GAAG,CAAC,CAAA;QACjB;YACC,OAAOA,KAAK,CAAA;KACb;CACD"}