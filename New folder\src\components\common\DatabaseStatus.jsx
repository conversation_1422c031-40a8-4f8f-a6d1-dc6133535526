import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faDatabase, faExclamationTriangle, faCheckCircle } from '@fortawesome/free-solid-svg-icons'
import { api } from '../../services/api'

const DatabaseStatus = ({ showWhenHealthy = false }) => {
  const { data: healthStatus, isError, error } = useQuery({
    queryKey: ['database', 'health'],
    queryFn: () => api.get('/health'),
    staleTime: 10 * 1000, // 10 seconds
    refetchInterval: 30 * 1000, // Check every 30 seconds
    retry: 2,
    retryDelay: 5000, // 5 second delay between retries
  })

  // Don't show anything if healthy and showWhenHealthy is false
  if (!isError && !showWhenHealthy) {
    return null
  }

  // Show error state
  if (isError) {
    const isDbError = error?.message?.includes('Access denied') || 
                     error?.message?.includes('ETIMEDOUT') ||
                     error?.message?.includes('ECONNREFUSED')

    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 mb-4">
        <div className="flex items-center">
          <FontAwesomeIcon 
            icon={faExclamationTriangle} 
            className="text-yellow-400 mr-3" 
          />
          <div>
            <p className="text-sm text-yellow-700 font-medium">
              Database Connection Issue
            </p>
            <p className="text-xs text-yellow-600 mt-1">
              {isDbError 
                ? 'Experiencing temporary database connectivity issues. The system is automatically retrying...'
                : 'Some features may be temporarily unavailable. Please try again in a moment.'
              }
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Show healthy state (only if showWhenHealthy is true)
  return (
    <div className="bg-green-50 border-l-4 border-green-400 p-3 mb-4">
      <div className="flex items-center">
        <FontAwesomeIcon 
          icon={faCheckCircle} 
          className="text-green-400 mr-3" 
        />
        <div>
          <p className="text-sm text-green-700 font-medium">
            System Status: Healthy
          </p>
          <p className="text-xs text-green-600 mt-1">
            All systems are operational
          </p>
        </div>
      </div>
    </div>
  )
}

export default DatabaseStatus
