{"version": 3, "sources": ["../../../src/hooks/useDrag/useDragSource.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react'\n\nimport type { Connector } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragSourceHookSpec } from '../types.js'\nimport { DragSourceImpl } from './DragSourceImpl.js'\n\nexport function useDragSource<O, R, P>(\n\tspec: DragSourceHookSpec<O, R, P>,\n\tmonitor: DragSourceMonitor<O, R>,\n\tconnector: Connector,\n) {\n\tconst handler = useMemo(\n\t\t() => new DragSourceImpl(spec, monitor, connector),\n\t\t[monitor, connector],\n\t)\n\tuseEffect(() => {\n\t\thandler.spec = spec\n\t}, [spec])\n\treturn handler\n}\n"], "names": ["useEffect", "useMemo", "DragSourceImpl", "useDragSource", "spec", "monitor", "connector", "handler"], "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,QAAQ,OAAO,CAAA;AAK1C,SAASC,cAAc,QAAQ,qBAAqB,CAAA;AAEpD,OAAO,SAASC,aAAa,CAC5BC,IAAiC,EACjCC,OAAgC,EAChCC,SAAoB,EACnB;IACD,MAAMC,OAAO,GAAGN,OAAO,CACtB,IAAM,IAAIC,cAAc,CAACE,IAAI,EAAEC,OAAO,EAAEC,SAAS,CAAC;IAAA,EAClD;QAACD,OAAO;QAAEC,SAAS;KAAC,CACpB;IACDN,SAAS,CAAC,IAAM;QACfO,OAAO,CAACH,IAAI,GAAGA,IAAI;KACnB,EAAE;QAACA,IAAI;KAAC,CAAC;IACV,OAAOG,OAAO,CAAA;CACd"}