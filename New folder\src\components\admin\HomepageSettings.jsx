import { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faSave,
  faHome,
  faPlus,
  faTrash,
  faImage,
  faLink,
  faEye,
  faSpinner
} from '@fortawesome/free-solid-svg-icons'
import toast from 'react-hot-toast'
import { useHomepageSettings, useUpdateHomepageSettings } from '../../hooks/useSettings'
import { useCategories } from '../../hooks/useCategories'
import FileUpload from '../common/FileUpload'
import LoadingSpinner from '../common/LoadingSpinner'

const HomepageSettings = () => {
  // React Query hooks
  const {
    data: homepageSettings,
    isLoading: settingsLoading,
    error: settingsError
  } = useHomepageSettings()

  const {
    data: categories = [],
    isLoading: categoriesLoading
  } = useCategories()

  const updateHomepageSettings = useUpdateHomepageSettings()

  const [settings, setSettings] = useState({
    heroTitle: '',
    heroDescription: '',
    heroImage: '',
    heroBackgroundType: 'gradient', // 'gradient', 'image', 'color'
    heroBackgroundValue: 'from-blue-600 via-purple-600 to-indigo-700',
    heroButtonText: 'Start Learning',
    heroButtonUrl: '/courses',
    iconCardsBackgroundType: 'color',
    iconCardsBackgroundValue: '#ffffff',
    categoriesBackgroundType: 'color',
    categoriesBackgroundValue: '#f9fafb',
    iconCards: [],
    featuredCategories: []
  })
  const [previewMode, setPreviewMode] = useState(false)

  const loading = settingsLoading || categoriesLoading || updateHomepageSettings.isPending

  // Update local settings when React Query data changes
  useEffect(() => {
    if (homepageSettings) {
      // Ensure all fields have default values to prevent controlled/uncontrolled input issues
      const safeSettings = {
        heroTitle: homepageSettings.heroTitle || '',
        heroDescription: homepageSettings.heroDescription || '',
        heroImage: homepageSettings.heroImage || '',
        heroBackgroundType: homepageSettings.heroBackgroundType || 'gradient',
        heroBackgroundValue: homepageSettings.heroBackgroundValue || 'from-blue-600 via-purple-600 to-indigo-700',
        heroButtonText: homepageSettings.heroButtonText || 'Start Learning',
        heroButtonUrl: homepageSettings.heroButtonUrl || '/courses',
        iconCardsBackgroundType: homepageSettings.iconCardsBackgroundType || 'color',
        iconCardsBackgroundValue: homepageSettings.iconCardsBackgroundValue || '#ffffff',
        categoriesBackgroundType: homepageSettings.categoriesBackgroundType || 'color',
        categoriesBackgroundValue: homepageSettings.categoriesBackgroundValue || '#f9fafb',
        iconCards: Array.isArray(homepageSettings.iconCards) ? homepageSettings.iconCards.map(card => ({
          icon: card.icon || 'faGraduationCap',
          title: card.title || '',
          description: card.description || '',
          url: card.url || '/courses'
        })) : [],
        featuredCategories: Array.isArray(homepageSettings.featuredCategories) ? homepageSettings.featuredCategories : []
      }

      setSettings(safeSettings)
    }
  }, [homepageSettings])

  const handleSave = async () => {
    try {
      await updateHomepageSettings.mutateAsync(settings)
      toast.success('Homepage settings saved successfully!')
    } catch (error) {
      console.error('Error saving homepage settings:', error)
      toast.error('Failed to save homepage settings')
    }
  }

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addIconCard = () => {
    const newCard = {
      icon: 'faGraduationCap',
      title: 'New Card',
      description: 'Card description',
      url: '/courses'
    }
    setSettings(prev => ({
      ...prev,
      iconCards: [...prev.iconCards, newCard]
    }))
  }

  const updateIconCard = (index, field, value) => {
    setSettings(prev => ({
      ...prev,
      iconCards: prev.iconCards.map((card, i) =>
        i === index ? { ...card, [field]: value } : card
      )
    }))
  }

  const removeIconCard = (index) => {
    setSettings(prev => ({
      ...prev,
      iconCards: prev.iconCards.filter((_, i) => i !== index)
    }))
  }

  const toggleCategory = (categoryId) => {
    setSettings(prev => ({
      ...prev,
      featuredCategories: prev.featuredCategories.includes(categoryId)
        ? prev.featuredCategories.filter(id => id !== categoryId)
        : [...prev.featuredCategories, categoryId]
    }))
  }

  const iconOptions = [
    { value: 'faGraduationCap', label: 'Graduation Cap' },
    { value: 'faUsers', label: 'Users' },
    { value: 'faTrophy', label: 'Trophy' },
    { value: 'faBook', label: 'Book' },
    { value: 'faChartLine', label: 'Chart' },
    { value: 'faPlay', label: 'Play' },
    { value: 'faCertificate', label: 'Certificate' },
    { value: 'faLightbulb', label: 'Lightbulb' }
  ]

  // Show loading spinner while initial data is loading
  if (settingsLoading || categoriesLoading) {
    return <LoadingSpinner />
  }

  // Show error message if there's an error loading data
  if (settingsError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Error Loading Settings
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Failed to load homepage settings. Please try again.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Reload Page
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Modern Header */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex items-center space-x-4 mb-4 lg:mb-0">
              <div className="p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                <FontAwesomeIcon icon={faHome} className="text-2xl text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                  Homepage Settings
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1 text-lg">
                  Customize your homepage appearance and content
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={() => setPreviewMode(!previewMode)}
                className="px-6 py-3 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2 border border-gray-200 dark:border-gray-700"
              >
                <FontAwesomeIcon icon={faEye} />
                <span className="font-medium">{previewMode ? 'Edit Mode' : 'Preview Mode'}</span>
              </button>
              <button
                onClick={handleSave}
                disabled={loading}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FontAwesomeIcon icon={loading ? faSpinner : faSave} className={loading ? 'animate-spin' : ''} />
                <span className="font-medium">{loading ? 'Saving...' : 'Save Changes'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Modern Card Layout */}
        <div className="grid gap-8">
          {/* Hero Section Settings */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <FontAwesomeIcon icon={faImage} className="mr-3" />
                Hero Section
              </h3>
              <p className="text-blue-100 mt-1">Configure your homepage hero section</p>
            </div>

            <div className="p-6">
              <div className="grid lg:grid-cols-3 gap-6">
                {/* Content Column */}
                <div className="lg:col-span-2 space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                        Hero Title
                      </label>
                      <input
                        type="text"
                        value={settings.heroTitle}
                        onChange={(e) => handleInputChange('heroTitle', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                        placeholder="Learn Without Limits"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                        Hero Button Text
                      </label>
                      <input
                        type="text"
                        value={settings.heroButtonText}
                        onChange={(e) => handleInputChange('heroButtonText', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                        placeholder="Start Learning"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Hero Description
                    </label>
                    <textarea
                      value={settings.heroDescription}
                      onChange={(e) => handleInputChange('heroDescription', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 h-24 resize-none"
                      placeholder="Discover thousands of courses..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Button URL
                    </label>
                    <input
                      type="text"
                      value={settings.heroButtonUrl}
                      onChange={(e) => handleInputChange('heroButtonUrl', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                      placeholder="/courses"
                    />
                  </div>
                </div>

                {/* Preview Column */}
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-full max-w-sm">
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Hero Image
                    </label>
                    <div className="relative group">
                      <div className="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-2xl overflow-hidden border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-400 transition-all duration-200">
                        {settings.heroImage ? (
                          <img
                            src={settings.heroImage}
                            alt="Hero Preview"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <div className="text-center">
                              <FontAwesomeIcon icon={faImage} className="text-4xl text-gray-400 mb-2" />
                              <p className="text-sm text-gray-500">No image selected</p>
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-2xl flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <FileUpload
                            onUpload={(url) => handleInputChange('heroImage', url)}
                            accept="image/*"
                            buttonText="Upload Image"
                            showPreview={false}
                            type="hero-images"
                            className="bg-white text-gray-700 hover:bg-gray-50"
                          />
                        </div>
                      </div>
                    </div>
                    <input
                      type="text"
                      value={settings.heroImage}
                      onChange={(e) => handleInputChange('heroImage', e.target.value)}
                      className="w-full px-4 py-2 mt-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                      placeholder="Or paste image URL"
                    />
                  </div>
                </div>
              </div>

              {/* Background Settings */}
              <div className="mt-6 p-6 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-gray-600 rounded-2xl border border-gray-200 dark:border-gray-600">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <FontAwesomeIcon icon={faImage} className="mr-2 text-blue-600" />
                  Background Settings
                </h4>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Background Type
                    </label>
                    <select
                      value={settings.heroBackgroundType}
                      onChange={(e) => handleInputChange('heroBackgroundType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                    >
                      <option value="gradient">Gradient</option>
                      <option value="color">Solid Color</option>
                      <option value="image">Background Image</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Background Value
                    </label>
                    {settings.heroBackgroundType === 'gradient' && (
                      <select
                        value={settings.heroBackgroundValue}
                        onChange={(e) => handleInputChange('heroBackgroundValue', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                      >
                        <option value="from-blue-600 via-purple-600 to-indigo-700">Blue to Purple</option>
                        <option value="from-purple-600 via-pink-600 to-red-600">Purple to Red</option>
                        <option value="from-green-600 via-blue-600 to-purple-600">Green to Purple</option>
                        <option value="from-yellow-600 via-orange-600 to-red-600">Yellow to Red</option>
                        <option value="from-gray-600 via-gray-700 to-gray-800">Gray Gradient</option>
                      </select>
                    )}

                    {settings.heroBackgroundType === 'color' && (
                      <div className="relative">
                        <input
                          type="color"
                          value={settings.heroBackgroundValue}
                          onChange={(e) => handleInputChange('heroBackgroundValue', e.target.value)}
                          className="w-full h-12 border border-gray-200 dark:border-gray-600 rounded-xl cursor-pointer"
                        />
                        <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                          <span className="text-sm text-gray-500">{settings.heroBackgroundValue}</span>
                        </div>
                      </div>
                    )}

                    {settings.heroBackgroundType === 'image' && (
                      <div className="space-y-3">
                        <input
                          type="text"
                          value={settings.heroBackgroundValue}
                          onChange={(e) => handleInputChange('heroBackgroundValue', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                          placeholder="Background image URL"
                        />
                        <div className="text-center text-gray-500 dark:text-gray-400">or</div>
                        <FileUpload
                          onUpload={(url) => handleInputChange('heroBackgroundValue', url)}
                          accept="image/*"
                          buttonText="Upload Background Image"
                          showPreview={false}
                          type="hero-backgrounds"
                          className="bg-white text-gray-700 hover:bg-gray-50"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Icon Cards Settings */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
            <div className="bg-gradient-to-r from-green-600 to-teal-600 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold text-white flex items-center">
                    <FontAwesomeIcon icon={faLink} className="mr-3" />
                    Icon Cards Section
                  </h3>
                  <p className="text-green-100 mt-1">Manage your homepage feature cards</p>
                </div>
                <button
                  onClick={addIconCard}
                  className="px-4 py-2 bg-white text-green-600 rounded-lg hover:bg-green-50 transition-colors duration-200 flex items-center space-x-2 font-medium"
                >
                  <FontAwesomeIcon icon={faPlus} />
                  <span>Add Card</span>
                </button>
              </div>
            </div>

            <div className="p-6">

              {/* Background Settings */}
              <div className="mb-6 p-6 bg-gradient-to-r from-green-50 to-teal-50 dark:from-gray-700 dark:to-gray-600 rounded-2xl border border-green-200 dark:border-gray-600">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <FontAwesomeIcon icon={faImage} className="mr-2 text-green-600" />
                  Background Settings
                </h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Background Type
                    </label>
                    <select
                      value={settings.iconCardsBackgroundType}
                      onChange={(e) => handleInputChange('iconCardsBackgroundType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                    >
                      <option value="color">Solid Color</option>
                      <option value="gradient">Gradient</option>
                      <option value="image">Background Image</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Background Value
                    </label>
                    {settings.iconCardsBackgroundType === 'color' && (
                      <div className="relative">
                        <input
                          type="color"
                          value={settings.iconCardsBackgroundValue}
                          onChange={(e) => handleInputChange('iconCardsBackgroundValue', e.target.value)}
                          className="w-full h-12 border border-gray-200 dark:border-gray-600 rounded-xl cursor-pointer"
                        />
                        <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                          <span className="text-sm text-gray-500">{settings.iconCardsBackgroundValue}</span>
                        </div>
                      </div>
                    )}
                    {settings.iconCardsBackgroundType === 'gradient' && (
                      <select
                        value={settings.iconCardsBackgroundValue}
                        onChange={(e) => handleInputChange('iconCardsBackgroundValue', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                      >
                        <option value="from-gray-50 to-white">Light Gray</option>
                        <option value="from-blue-50 to-indigo-100">Light Blue</option>
                        <option value="from-purple-50 to-pink-100">Light Purple</option>
                        <option value="from-green-50 to-emerald-100">Light Green</option>
                      </select>
                    )}
                    {settings.iconCardsBackgroundType === 'image' && (
                      <div className="space-y-3">
                        <input
                          type="text"
                          value={settings.iconCardsBackgroundValue}
                          onChange={(e) => handleInputChange('iconCardsBackgroundValue', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                          placeholder="Background image URL"
                        />
                        <div className="text-center text-gray-500 dark:text-gray-400">or</div>
                        <FileUpload
                          onUpload={(url) => handleInputChange('iconCardsBackgroundValue', url)}
                          accept="image/*"
                          buttonText="Upload Background Image"
                          showPreview={false}
                          type="iconcard-backgrounds"
                          className="bg-white text-gray-700 hover:bg-gray-50"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Icon Cards */}
              <div className="space-y-6">
                {settings.iconCards.map((card, index) => (
                  <div key={index} className="bg-gradient-to-r from-white to-gray-50 dark:from-gray-700 dark:to-gray-600 border border-gray-200 dark:border-gray-600 rounded-2xl p-6 shadow-md hover:shadow-lg transition-all duration-200">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-white font-bold text-sm">{index + 1}</span>
                        </div>
                        Card {index + 1}
                      </h4>
                      <button
                        onClick={() => removeIconCard(index)}
                        className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </button>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                          Icon
                        </label>
                        <select
                          value={card.icon}
                          onChange={(e) => updateIconCard(index, 'icon', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                        >
                          {iconOptions.map(option => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                          Title
                        </label>
                        <input
                          type="text"
                          value={card.title}
                          onChange={(e) => updateIconCard(index, 'title', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                          placeholder="Card title"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                          Description
                        </label>
                        <input
                          type="text"
                          value={card.description}
                          onChange={(e) => updateIconCard(index, 'description', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                          placeholder="Card description"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                          URL
                        </label>
                        <input
                          type="text"
                          value={card.url}
                          onChange={(e) => updateIconCard(index, 'url', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                          placeholder="/courses"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Featured Categories */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <FontAwesomeIcon icon={faEye} className="mr-3" />
                Featured Categories Section
              </h3>
              <p className="text-purple-100 mt-1">Select categories to showcase on your homepage</p>
            </div>

            <div className="p-6">
              {/* Categories Background Settings */}
              <div className="mb-6 p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-gray-700 dark:to-gray-600 rounded-2xl border border-purple-200 dark:border-gray-600">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <FontAwesomeIcon icon={faImage} className="mr-2 text-purple-600" />
                  Background Settings
                </h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Background Type
                    </label>
                    <select
                      value={settings.categoriesBackgroundType}
                      onChange={(e) => handleInputChange('categoriesBackgroundType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                    >
                      <option value="color">Solid Color</option>
                      <option value="gradient">Gradient</option>
                      <option value="image">Background Image</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                      Background Value
                    </label>
                    {settings.categoriesBackgroundType === 'color' && (
                      <div className="relative">
                        <input
                          type="color"
                          value={settings.categoriesBackgroundValue}
                          onChange={(e) => handleInputChange('categoriesBackgroundValue', e.target.value)}
                          className="w-full h-12 border border-gray-200 dark:border-gray-600 rounded-xl cursor-pointer"
                        />
                        <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                          <span className="text-sm text-gray-500">{settings.categoriesBackgroundValue}</span>
                        </div>
                      </div>
                    )}
                    {settings.categoriesBackgroundType === 'gradient' && (
                      <select
                        value={settings.categoriesBackgroundValue}
                        onChange={(e) => handleInputChange('categoriesBackgroundValue', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                      >
                        <option value="from-gray-50 to-gray-100">Light Gray</option>
                        <option value="from-blue-50 to-blue-100">Light Blue</option>
                        <option value="from-purple-50 to-purple-100">Light Purple</option>
                        <option value="from-green-50 to-green-100">Light Green</option>
                      </select>
                    )}
                    {settings.categoriesBackgroundType === 'image' && (
                      <div className="space-y-3">
                        <input
                          type="text"
                          value={settings.categoriesBackgroundValue}
                          onChange={(e) => handleInputChange('categoriesBackgroundValue', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                          placeholder="Background image URL"
                        />
                        <div className="text-center text-gray-500 dark:text-gray-400">or</div>
                        <FileUpload
                          onUpload={(url) => handleInputChange('categoriesBackgroundValue', url)}
                          accept="image/*"
                          buttonText="Upload Background Image"
                          showPreview={false}
                          type="category-backgrounds"
                          className="bg-white text-gray-700 hover:bg-gray-50"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-gray-600 dark:text-gray-400 mb-4 text-center">
                  Select which categories to display on the homepage
                </p>

                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Array.isArray(categories) && categories.length > 0 ? (
                    categories.map(category => (
                      <div
                        key={category.id}
                        className={`border-2 rounded-2xl p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                          settings.featuredCategories.includes(category.id)
                            ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20 shadow-md'
                            : 'border-gray-200 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-500'
                        }`}
                        onClick={() => toggleCategory(category.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            <input
                              type="checkbox"
                              checked={settings.featuredCategories.includes(category.id)}
                              onChange={() => toggleCategory(category.id)}
                              className="w-5 h-5 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                            />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                              {category.name}
                            </h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {category.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-full text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">
                        No categories available. Create some categories first to feature them on the homepage.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomepageSettings
