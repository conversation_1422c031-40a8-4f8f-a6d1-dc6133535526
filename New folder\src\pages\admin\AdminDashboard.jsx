import React from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faUsers,
  faGraduationCap,
  faQuestionCircle,
  faChartLine,
  faPlus,
  faEye,
  faCog,
  faUserGraduate,
  faChalkboardTeacher,
  faTicketAlt
} from '@fortawesome/free-solid-svg-icons'
import Header from '../../components/common/Header'
import LoadingSpinner from '../../components/common/LoadingSpinner'
import { useCourses, useUsers } from '../../hooks'

const AdminDashboard = () => {
  // Use React Query hooks for data fetching
  const {
    data: courses = [],
    isLoading: coursesLoading,
    error: coursesError
  } = useCourses()

  const {
    data: users = [],
    isLoading: usersLoading,
    error: usersError
  } = useUsers()

  // Calculate derived data with safety checks
  const loading = coursesLoading || usersLoading

  // Ensure arrays are properly initialized
  const safeUsers = Array.isArray(users) ? users : []
  const safeCourses = Array.isArray(courses) ? courses : []

  const students = safeUsers.filter(user => user.role === 'student')
  const instructors = safeUsers.filter(user => user.role === 'instructor')

  const stats = {
    totalUsers: safeUsers.length,
    totalCourses: safeCourses.length,
    totalQuizzes: 0, // Would need a separate query for quizzes
    totalInstructors: instructors.length,
    totalStudents: students.length,
    activeUsers: safeUsers.filter(user => user.isActive).length
  }

  const recentCourses = safeCourses.slice(0, 5)
  const recentUsers = safeUsers.slice(0, 5)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  // Show error state if both queries failed
  if (coursesError && usersError) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400 mb-4">
              Failed to load dashboard data
            </p>
            <button
              onClick={() => window.location.reload()}
              className="btn btn-primary"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your educational platform and monitor system performance.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="card"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
                <FontAwesomeIcon icon={faUsers} className="text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.totalUsers}
                </p>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Total Users
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="card"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4">
                <FontAwesomeIcon icon={faGraduationCap} className="text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.totalCourses}
                </p>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Total Courses
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="card"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-4">
                <FontAwesomeIcon icon={faChalkboardTeacher} className="text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.totalInstructors}
                </p>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Instructors
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="card"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mr-4">
                <FontAwesomeIcon icon={faUserGraduate} className="text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stats.totalStudents}
                </p>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Students
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="card mb-8"
        >
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              to="/admin/courses/create"
              className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
            >
              <FontAwesomeIcon icon={faPlus} className="text-primary-600" />
              <span className="text-gray-900 dark:text-white font-medium">Create Course</span>
            </Link>
            <Link
              to="/admin/courses"
              className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
            >
              <FontAwesomeIcon icon={faGraduationCap} className="text-green-600" />
              <span className="text-gray-900 dark:text-white font-medium">Manage Courses</span>
            </Link>
            <Link
              to="/admin/payment-codes"
              className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
            >
              <FontAwesomeIcon icon={faTicketAlt} className="text-pink-600" />
              <span className="text-gray-900 dark:text-white font-medium">Payment Codes</span>
            </Link>
            <Link
              to="/admin/panel"
              className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
            >
              <FontAwesomeIcon icon={faCog} className="text-blue-600" />
              <span className="text-gray-900 dark:text-white font-medium">System Settings</span>
            </Link>
            <Link
              to="/admin/users"
              className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
            >
              <FontAwesomeIcon icon={faUsers} className="text-purple-600" />
              <span className="text-gray-900 dark:text-white font-medium">Manage Users</span>
            </Link>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Recent Courses */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="card"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Recent Courses
              </h2>
              <Link
                to="/admin/courses"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                View All
              </Link>
            </div>
            {recentCourses.length > 0 ? (
              <div className="space-y-3">
                {recentCourses.map((course) => (
                  <div
                    key={course.id}
                    className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
                  >
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {course.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        By {course.instructor}
                      </p>
                    </div>
                    <Link
                      to={`/admin/courses/${course.id}/edit`}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      <FontAwesomeIcon icon={faEye} />
                    </Link>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-600 dark:text-gray-400 text-center py-4">
                No courses created yet.
              </p>
            )}
          </motion.div>

          {/* Recent Users */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="card"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Recent Users
              </h2>
              <Link
                to="/admin/users"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                View All
              </Link>
            </div>
            {recentUsers.length > 0 ? (
              <div className="space-y-3">
                {recentUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {user.name?.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {user.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {user.role}
                        </p>
                      </div>
                    </div>
                    <span className={`badge ${
                      user.isActive ? 'badge-success' : 'badge-danger'
                    }`}>
                      {user.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-600 dark:text-gray-400 text-center py-4">
                No users registered yet.
              </p>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
