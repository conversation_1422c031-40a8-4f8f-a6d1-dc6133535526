{"version": 3, "sources": ["../../src/utils/dirtiness.ts"], "sourcesContent": ["import { intersection } from './js_utils.js'\n\nexport const NONE: string[] = []\nexport const ALL: string[] = []\n// Add these flags for debug\n;(NONE as any).__IS_NONE__ = true\n;(ALL as any).__IS_ALL__ = true\n\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */\nexport function areDirty(\n\tdirtyIds: string[],\n\thandlerIds: string[] | undefined,\n): boolean {\n\tif (dirtyIds === NONE) {\n\t\treturn false\n\t}\n\n\tif (dirtyIds === ALL || typeof handlerIds === 'undefined') {\n\t\treturn true\n\t}\n\n\tconst commonIds = intersection(handlerIds, dirtyIds)\n\treturn commonIds.length > 0\n}\n"], "names": ["intersection", "NONE", "ALL", "__IS_NONE__", "__IS_ALL__", "areDirty", "dirtyIds", "handlerIds", "commonIds", "length"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAe,CAAA;AAE5C,OAAO,MAAMC,IAAI,GAAa,EAAE,CAAA;AAChC,OAAO,MAAMC,GAAG,GAAa,EAAE,CAE9B;AAAA,AAACD,IAAI,CAASE,WAAW,GAAG,IAAI,CAChC;AAAA,AAACD,GAAG,CAASE,UAAU,GAAG,IAAI;AAE/B;;;;;GAKG,CACH,OAAO,SAASC,QAAQ,CACvBC,QAAkB,EAClBC,UAAgC,EACtB;IACV,IAAID,QAAQ,KAAKL,IAAI,EAAE;QACtB,OAAO,KAAK,CAAA;KACZ;IAED,IAAIK,QAAQ,KAAKJ,GAAG,IAAI,OAAOK,UAAU,KAAK,WAAW,EAAE;QAC1D,OAAO,IAAI,CAAA;KACX;IAED,MAAMC,SAAS,GAAGR,YAAY,CAACO,UAAU,EAAED,QAAQ,CAAC;IACpD,OAAOE,SAAS,CAACC,MAAM,GAAG,CAAC,CAAA;CAC3B"}