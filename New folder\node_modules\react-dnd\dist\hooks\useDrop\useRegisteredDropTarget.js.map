{"version": 3, "sources": ["../../../src/hooks/useDrop/useRegisteredDropTarget.ts"], "sourcesContent": ["import type { TargetConnector } from '../../internals/index.js'\nimport { registerTarget } from '../../internals/index.js'\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\nimport { useAccept } from './useAccept.js'\nimport { useDropTarget } from './useDropTarget.js'\n\nexport function useRegisteredDropTarget<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n\tmonitor: DropTargetMonitor<O, R>,\n\tconnector: TargetConnector,\n): void {\n\tconst manager = useDragDropManager()\n\tconst dropTarget = useDropTarget(spec, monitor)\n\tconst accept = useAccept(spec)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction registerDropTarget() {\n\t\t\tconst [handlerId, unregister] = registerTarget(\n\t\t\t\taccept,\n\t\t\t\tdropTarget,\n\t\t\t\tmanager,\n\t\t\t)\n\t\t\tmonitor.receiveHandlerId(handlerId)\n\t\t\tconnector.receiveHandlerId(handlerId)\n\t\t\treturn unregister\n\t\t},\n\t\t[\n\t\t\tmanager,\n\t\t\tmonitor,\n\t\t\tdropTarget,\n\t\t\tconnector,\n\t\t\taccept.map((a) => a.toString()).join('|'),\n\t\t],\n\t)\n}\n"], "names": ["registerTarget", "useDragDropManager", "useIsomorphicLayoutEffect", "useAccept", "useDropTarget", "useRegisteredDropTarget", "spec", "monitor", "connector", "manager", "drop<PERSON>ar<PERSON>", "accept", "registerDropTarget", "handlerId", "unregister", "receiveHandlerId", "map", "a", "toString", "join"], "mappings": "AACA,SAASA,cAAc,QAAQ,0BAA0B,CAAA;AAGzD,SAASC,kBAAkB,QAAQ,0BAA0B,CAAA;AAC7D,SAASC,yBAAyB,QAAQ,iCAAiC,CAAA;AAC3E,SAASC,SAAS,QAAQ,gBAAgB,CAAA;AAC1C,SAASC,aAAa,QAAQ,oBAAoB,CAAA;AAElD,OAAO,SAASC,uBAAuB,CACtCC,IAAiC,EACjCC,OAAgC,EAChCC,SAA0B,EACnB;IACP,MAAMC,OAAO,GAAGR,kBAAkB,EAAE;IACpC,MAAMS,UAAU,GAAGN,aAAa,CAACE,IAAI,EAAEC,OAAO,CAAC;IAC/C,MAAMI,MAAM,GAAGR,SAAS,CAACG,IAAI,CAAC;IAE9BJ,yBAAyB,CACxB,SAASU,kBAAkB,GAAG;QAC7B,MAAM,CAACC,SAAS,EAAEC,UAAU,CAAC,GAAGd,cAAc,CAC7CW,MAAM,EACND,UAAU,EACVD,OAAO,CACP;QACDF,OAAO,CAACQ,gBAAgB,CAACF,SAAS,CAAC;QACnCL,SAAS,CAACO,gBAAgB,CAACF,SAAS,CAAC;QACrC,OAAOC,UAAU,CAAA;KACjB,EACD;QACCL,OAAO;QACPF,OAAO;QACPG,UAAU;QACVF,SAAS;QACTG,MAAM,CAACK,GAAG,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,QAAQ,EAAE;QAAA,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;KACzC,CACD;CACD"}