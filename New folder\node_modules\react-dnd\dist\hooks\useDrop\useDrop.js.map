{"version": 3, "sources": ["../../../src/hooks/useDrop/useDrop.ts"], "sourcesContent": ["import type { ConnectDropTarget } from '../../types/index.js'\nimport type { DropTargetHookSpec, FactoryOrInstance } from '../types.js'\nimport { useCollectedProps } from '../useCollectedProps.js'\nimport { useOptionalFactory } from '../useOptionalFactory.js'\nimport { useConnectDropTarget } from './connectors.js'\nimport { useDropTargetConnector } from './useDropTargetConnector.js'\nimport { useDropTargetMonitor } from './useDropTargetMonitor.js'\nimport { useRegisteredDropTarget } from './useRegisteredDropTarget.js'\n\n/**\n * useDropTarget Hook\n * @param spec The drop target specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrop<\n\tDragObject = unknown,\n\tDropResult = unknown,\n\tCollectedProps = unknown,\n>(\n\tspecArg: FactoryOrInstance<\n\t\tDropTargetHookSpec<DragObject, DropResult, CollectedProps>\n\t>,\n\tdeps?: unknown[],\n): [CollectedProps, ConnectDropTarget] {\n\tconst spec = useOptionalFactory(specArg, deps)\n\tconst monitor = useDropTargetMonitor<DragObject, DropResult>()\n\tconst connector = useDropTargetConnector(spec.options)\n\tuseRegisteredDropTarget(spec, monitor, connector)\n\n\treturn [\n\t\tuseCollectedProps(spec.collect, monitor, connector),\n\t\tuseConnectDropTarget(connector),\n\t]\n}\n"], "names": ["useCollectedProps", "useOptionalFactory", "useConnectDropTarget", "useDropTargetConnector", "useDropTargetMonitor", "useRegisteredDropTarget", "useDrop", "specArg", "deps", "spec", "monitor", "connector", "options", "collect"], "mappings": "AAEA,SAASA,iBAAiB,QAAQ,yBAAyB,CAAA;AAC3D,SAASC,kBAAkB,QAAQ,0BAA0B,CAAA;AAC7D,SAASC,oBAAoB,QAAQ,iBAAiB,CAAA;AACtD,SAASC,sBAAsB,QAAQ,6BAA6B,CAAA;AACpE,SAASC,oBAAoB,QAAQ,2BAA2B,CAAA;AAChE,SAASC,uBAAuB,QAAQ,8BAA8B,CAAA;AAEtE;;;;GAIG,CACH,OAAO,SAASC,OAAO,CAKtBC,OAEC,EACDC,IAAgB,EACsB;IACtC,MAAMC,IAAI,GAAGR,kBAAkB,CAACM,OAAO,EAAEC,IAAI,CAAC;IAC9C,MAAME,OAAO,GAAGN,oBAAoB,EAA0B;IAC9D,MAAMO,SAAS,GAAGR,sBAAsB,CAACM,IAAI,CAACG,OAAO,CAAC;IACtDP,uBAAuB,CAACI,IAAI,EAAEC,OAAO,EAAEC,SAAS,CAAC;IAEjD,OAAO;QACNX,iBAAiB,CAACS,IAAI,CAACI,OAAO,EAAEH,OAAO,EAAEC,SAAS,CAAC;QACnDT,oBAAoB,CAACS,SAAS,CAAC;KAC/B,CAAA;CACD"}