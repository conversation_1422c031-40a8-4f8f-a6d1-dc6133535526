import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { quizService } from '../services/api'
import { queryKeys } from '../config/reactQuery'
import toast from 'react-hot-toast'

// Get quizzes by section
export const useQuizzesBySection = (sectionId) => {
  return useQuery({
    queryKey: queryKeys.quizzes.bySection(sectionId),
    queryFn: () => quizService.getBySection(sectionId),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!sectionId,
  })
}

// Get quizzes by course
export const useQuizzesByCourse = (courseId) => {
  return useQuery({
    queryKey: queryKeys.quizzes.byCourse(courseId),
    queryFn: () => quizService.getByCourse(courseId),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!courseId,
  })
}

// Get quiz by ID
export const useQuiz = (id) => {
  return useQuery({
    queryKey: queryKeys.quizzes.detail(id),
    queryFn: () => quizService.getById(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
  })
}

// Get quiz for editing
export const useQuizForEdit = (id) => {
  return useQuery({
    queryKey: queryKeys.quizzes.edit(id),
    queryFn: () => quizService.getQuizForEdit(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
  })
}

// Get quiz results
export const useQuizResults = (id) => {
  return useQuery({
    queryKey: queryKeys.quizzes.results(id),
    queryFn: () => quizService.getResults(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
  })
}

// Create quiz mutation
export const useCreateQuiz = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: quizService.create,
    onSuccess: (data) => {
      // Invalidate related queries
      if (data.section_id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.quizzes.bySection(data.section_id) })
      }
      if (data.course_id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.quizzes.byCourse(data.course_id) })
        queryClient.invalidateQueries({ queryKey: queryKeys.courses.curriculum(data.course_id) })
      }
      
      toast.success('Quiz created successfully!')
      return data
    },
    onError: (error) => {
      console.error('Create quiz error:', error)
      toast.error(error.message || 'Failed to create quiz')
    },
  })
}

// Update quiz mutation
export const useUpdateQuiz = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, quizData }) => quizService.update(id, quizData),
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.quizzes.detail(variables.id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.quizzes.edit(variables.id) })
      
      if (data.section_id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.quizzes.bySection(data.section_id) })
      }
      if (data.course_id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.quizzes.byCourse(data.course_id) })
        queryClient.invalidateQueries({ queryKey: queryKeys.courses.curriculum(data.course_id) })
      }
      
      toast.success('Quiz updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update quiz error:', error)
      toast.error(error.message || 'Failed to update quiz')
    },
  })
}

// Delete quiz mutation
export const useDeleteQuiz = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: quizService.delete,
    onSuccess: (data, quizId) => {
      // Remove quiz from cache
      queryClient.removeQueries({ queryKey: queryKeys.quizzes.detail(quizId) })
      queryClient.removeQueries({ queryKey: queryKeys.quizzes.edit(quizId) })
      queryClient.removeQueries({ queryKey: queryKeys.quizzes.results(quizId) })
      
      // Invalidate section and course queries
      queryClient.invalidateQueries({ queryKey: ['quizzes'] })
      queryClient.invalidateQueries({ queryKey: ['courses'] })
      
      toast.success('Quiz deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete quiz error:', error)
      toast.error(error.message || 'Failed to delete quiz')
    },
  })
}

// Submit quiz mutation
export const useSubmitQuiz = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, answers }) => quizService.submit(id, answers),
    onSuccess: (data, variables) => {
      // Invalidate quiz results and user dashboard
      queryClient.invalidateQueries({ queryKey: queryKeys.quizzes.results(variables.id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.quizAttempts })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.statistics })
      
      toast.success('Quiz submitted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Submit quiz error:', error)
      toast.error(error.message || 'Failed to submit quiz')
    },
  })
}
