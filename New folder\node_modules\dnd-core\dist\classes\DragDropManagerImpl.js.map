{"version": 3, "sources": ["../../src/classes/DragDropManagerImpl.ts"], "sourcesContent": ["import type { Action, Store } from 'redux'\n\nimport { createDragDropActions } from '../actions/dragDrop/index.js'\nimport type {\n\tActionCreator,\n\tBackend,\n\tDragDropActions,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n} from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport type { DragDropMonitorImpl } from './DragDropMonitorImpl.js'\n\nexport class DragDropManagerImpl implements DragDropManager {\n\tprivate store: Store<State>\n\tprivate monitor: DragDropMonitor\n\tprivate backend: Backend | undefined\n\tprivate isSetUp = false\n\n\tpublic constructor(store: Store<State>, monitor: DragDropMonitor) {\n\t\tthis.store = store\n\t\tthis.monitor = monitor\n\t\tstore.subscribe(this.handleRefCountChange)\n\t}\n\n\tpublic receiveBackend(backend: Backend): void {\n\t\tthis.backend = backend\n\t}\n\n\tpublic getMonitor(): DragDropMonitor {\n\t\treturn this.monitor\n\t}\n\n\tpublic getBackend(): Backend {\n\t\treturn this.backend as Backend\n\t}\n\n\tpublic getRegistry(): HandlerRegistry {\n\t\treturn (this.monitor as DragDropMonitorImpl).registry\n\t}\n\n\tpublic getActions(): DragDropActions {\n\t\t/* eslint-disable-next-line @typescript-eslint/no-this-alias */\n\t\tconst manager = this\n\t\tconst { dispatch } = this.store\n\n\t\tfunction bindActionCreator(actionCreator: ActionCreator<any>) {\n\t\t\treturn (...args: any[]) => {\n\t\t\t\tconst action = actionCreator.apply(manager, args as any)\n\t\t\t\tif (typeof action !== 'undefined') {\n\t\t\t\t\tdispatch(action)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst actions = createDragDropActions(this)\n\n\t\treturn Object.keys(actions).reduce(\n\t\t\t(boundActions: DragDropActions, key: string) => {\n\t\t\t\tconst action: ActionCreator<any> = (actions as any)[\n\t\t\t\t\tkey\n\t\t\t\t] as ActionCreator<any>\n\t\t\t\t;(boundActions as any)[key] = bindActionCreator(action)\n\t\t\t\treturn boundActions\n\t\t\t},\n\t\t\t{} as DragDropActions,\n\t\t)\n\t}\n\n\tpublic dispatch(action: Action<any>): void {\n\t\tthis.store.dispatch(action)\n\t}\n\n\tprivate handleRefCountChange = (): void => {\n\t\tconst shouldSetUp = this.store.getState().refCount > 0\n\t\tif (this.backend) {\n\t\t\tif (shouldSetUp && !this.isSetUp) {\n\t\t\t\tthis.backend.setup()\n\t\t\t\tthis.isSetUp = true\n\t\t\t} else if (!shouldSetUp && this.isSetUp) {\n\t\t\t\tthis.backend.teardown()\n\t\t\t\tthis.isSetUp = false\n\t\t\t}\n\t\t}\n\t}\n}\n"], "names": ["createDragDropActions", "DragDropManagerImpl", "receiveBackend", "backend", "getMonitor", "monitor", "getBackend", "getRegistry", "registry", "getActions", "manager", "dispatch", "store", "bindActionCreator", "actionCreator", "args", "action", "apply", "actions", "Object", "keys", "reduce", "boundActions", "key", "isSetUp", "handleRefCountChange", "shouldSetUp", "getState", "refCount", "setup", "teardown", "subscribe"], "mappings": "AAEA,SAASA,qBAAqB,QAAQ,8BAA8B,CAAA;AAYpE,OAAO,MAAMC,mBAAmB;IAY/B,AAAOC,cAAc,CAACC,OAAgB,EAAQ;QAC7C,IAAI,CAACA,OAAO,GAAGA,OAAO;KACtB;IAED,AAAOC,UAAU,GAAoB;QACpC,OAAO,IAAI,CAACC,OAAO,CAAA;KACnB;IAED,AAAOC,UAAU,GAAY;QAC5B,OAAO,IAAI,CAACH,OAAO,CAAW;KAC9B;IAED,AAAOI,WAAW,GAAoB;QACrC,OAAO,AAAC,IAAI,CAACF,OAAO,CAAyBG,QAAQ,CAAA;KACrD;IAED,AAAOC,UAAU,GAAoB;QACpC,+DAA+D,CAC/D,MAAMC,OAAO,GAAG,IAAI;QACpB,MAAM,EAAEC,QAAQ,CAAA,EAAE,GAAG,IAAI,CAACC,KAAK;QAE/B,SAASC,iBAAiB,CAACC,aAAiC,EAAE;YAC7D,OAAO,CAAC,GAAGC,IAAI,AAAO,GAAK;gBAC1B,MAAMC,MAAM,GAAGF,aAAa,CAACG,KAAK,CAACP,OAAO,EAAEK,IAAI,CAAQ;gBACxD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;oBAClCL,QAAQ,CAACK,MAAM,CAAC;iBAChB;aACD,CAAA;SACD;QAED,MAAME,OAAO,GAAGlB,qBAAqB,CAAC,IAAI,CAAC;QAE3C,OAAOmB,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,CACjC,CAACC,YAA6B,EAAEC,GAAW,GAAK;YAC/C,MAAMP,MAAM,GAAuB,AAACE,OAAO,AAAQ,CAClDK,GAAG,CACH,AAAsB,AACtB;YAAA,AAACD,YAAY,AAAQ,CAACC,GAAG,CAAC,GAAGV,iBAAiB,CAACG,MAAM,CAAC;YACvD,OAAOM,YAAY,CAAA;SACnB,EACD,EAAE,CACF,CAAA;KACD;IAED,AAAOX,QAAQ,CAACK,MAAmB,EAAQ;QAC1C,IAAI,CAACJ,KAAK,CAACD,QAAQ,CAACK,MAAM,CAAC;KAC3B;IApDD,YAAmBJ,KAAmB,EAAEP,OAAwB,CAAE;QAFlE,KAAQmB,OAAO,GAAG,KAAK,AAlBxB,CAkBwB;QAwDvB,KAAQC,oBAAoB,GAAG,IAAY;YAC1C,MAAMC,WAAW,GAAG,IAAI,CAACd,KAAK,CAACe,QAAQ,EAAE,CAACC,QAAQ,GAAG,CAAC;YACtD,IAAI,IAAI,CAACzB,OAAO,EAAE;gBACjB,IAAIuB,WAAW,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;oBACjC,IAAI,CAACrB,OAAO,CAAC0B,KAAK,EAAE;oBACpB,IAAI,CAACL,OAAO,GAAG,IAAI;iBACnB,MAAM,IAAI,CAACE,WAAW,IAAI,IAAI,CAACF,OAAO,EAAE;oBACxC,IAAI,CAACrB,OAAO,CAAC2B,QAAQ,EAAE;oBACvB,IAAI,CAACN,OAAO,GAAG,KAAK;iBACpB;aACD;SACD,AArFF,CAqFE;QAhEA,IAAI,CAACZ,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACP,OAAO,GAAGA,OAAO;QACtBO,KAAK,CAACmB,SAAS,CAAC,IAAI,CAACN,oBAAoB,CAAC;KAC1C;CA8DD"}