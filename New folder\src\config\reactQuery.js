import { QueryClient } from '@tanstack/react-query'

// Create QueryClient with optimized configuration
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Set staleTime to 30 seconds as requested
      staleTime: 30 * 1000, // 30 seconds
      
      // Cache time - how long data stays in cache when not being used
      gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)
      
      // Retry configuration
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false
        }

        // Check for database connection errors
        const isDbError = error?.message?.includes('Access denied') ||
                         error?.message?.includes('ETIMEDOUT') ||
                         error?.message?.includes('ECONNREFUSED') ||
                         error?.code === 'NETWORK_ERROR'

        // Retry database errors more aggressively
        if (isDbError) {
          return failureCount < 5
        }

        // Retry up to 3 times for other errors
        return failureCount < 3
      },
      
      // Retry delay with exponential backoff (longer for database errors)
      retryDelay: (attemptIndex, error) => {
        const isDbError = error?.message?.includes('Access denied') ||
                         error?.message?.includes('ETIMEDOUT') ||
                         error?.message?.includes('ECONNREFUSED')

        // Longer delay for database connection issues
        const baseDelay = isDbError ? 2000 : 1000
        return Math.min(baseDelay * 2 ** attemptIndex, 30000)
      },
      
      // Refetch configuration
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
      refetchOnMount: true,
      
      // Network mode
      networkMode: 'online',
      
      // Error handling
      throwOnError: false,
    },
    mutations: {
      // Retry mutations once on network errors
      retry: (failureCount, error) => {
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false
        }
        return failureCount < 1
      },
      
      // Network mode for mutations
      networkMode: 'online',
      
      // Error handling
      throwOnError: false,
    },
  },
})

// Query keys factory for consistent key management
export const queryKeys = {
  // Auth
  auth: {
    user: ['auth', 'user'],
    verify: (token) => ['auth', 'verify', token],
  },
  
  // Courses
  courses: {
    all: ['courses'],
    management: ['courses', 'management'],
    detail: (id) => ['courses', 'detail', id],
    curriculum: (id) => ['courses', 'curriculum', id],
    sections: (id) => ['courses', 'sections', id],
  },
  
  // Categories
  categories: {
    all: ['categories'],
    admin: ['categories', 'admin'],
    detail: (id) => ['categories', 'detail', id],
  },
  
  // Course Levels
  courseLevels: {
    all: ['course-levels'],
    admin: ['course-levels', 'admin'],
    detail: (id) => ['course-levels', 'detail', id],
  },
  
  // Course Languages
  courseLanguages: {
    all: ['course-languages'],
    admin: ['course-languages', 'admin'],
    detail: (id) => ['course-languages', 'detail', id],
  },
  
  // Course Classes
  courseClasses: {
    all: ['course-classes'],
    admin: ['course-classes', 'admin'],
    detail: (id) => ['course-classes', 'detail', id],
  },
  
  // Lessons
  lessons: {
    bySection: (sectionId) => ['lessons', 'section', sectionId],
    detail: (id) => ['lessons', 'detail', id],
  },
  
  // Quizzes
  quizzes: {
    bySection: (sectionId) => ['quizzes', 'section', sectionId],
    byCourse: (courseId) => ['quizzes', 'course', courseId],
    detail: (id) => ['quizzes', 'detail', id],
    edit: (id) => ['quizzes', 'edit', id],
    results: (id) => ['quizzes', 'results', id],
  },
  
  // Users
  users: {
    all: ['users'],
    profile: ['users', 'profile'],
    detail: (id) => ['users', 'detail', id],
    dashboard: {
      statistics: ['users', 'dashboard', 'statistics'],
      enrollments: ['users', 'dashboard', 'enrollments'],
      quizAttempts: ['users', 'dashboard', 'quiz-attempts'],
    },
    banned: ['users', 'banned'],
  },
  
  // Settings
  settings: {
    homepage: ['settings', 'homepage'],
    watermark: ['settings', 'watermark'],
    all: ['settings', 'all'],
    notifications: ['settings', 'notifications'],
    system: ['settings', 'system'],
    site: ['settings', 'site'],
    currencies: ['settings', 'currencies'],
    public: ['settings', 'public'],
  },
  
  // Payment
  payment: {
    wallet: {
      balance: ['payment', 'wallet', 'balance'],
      transactions: (page, limit) => ['payment', 'wallet', 'transactions', page, limit],
    },
    codes: {
      all: (params) => ['payment', 'codes', 'all', params],
      settings: ['payment', 'codes', 'settings'],
    },
  },
  
  // Notifications
  notifications: {
    all: ['notifications'],
    active: (page) => ['notifications', 'active', page],
  },
  
  // Files
  files: {
    upload: ['files', 'upload'],
  },
  
  // Admin Dashboard
  admin: {
    dashboard: ['admin', 'dashboard'],
    stats: ['admin', 'stats'],
  },
  
  // Site Context
  site: {
    settings: ['site', 'settings'],
  },
}

// Utility functions for cache management
export const cacheUtils = {
  // Invalidate all queries for a specific entity
  invalidateEntity: (entity) => {
    return queryClient.invalidateQueries({ queryKey: [entity] })
  },
  
  // Invalidate specific query
  invalidateQuery: (queryKey) => {
    return queryClient.invalidateQueries({ queryKey })
  },
  
  // Remove all cached data
  clearCache: () => {
    return queryClient.clear()
  },
  
  // Get cached data
  getCachedData: (queryKey) => {
    return queryClient.getQueryData(queryKey)
  },
  
  // Set cached data
  setCachedData: (queryKey, data) => {
    return queryClient.setQueryData(queryKey, data)
  },
  
  // Prefetch data
  prefetch: (queryKey, queryFn, options = {}) => {
    return queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: 30 * 1000, // 30 seconds
      ...options,
    })
  },
}

export default queryClient
