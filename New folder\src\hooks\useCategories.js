import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { categoryService } from '../services/api'
import { queryKeys } from '../config/reactQuery'
import toast from 'react-hot-toast'

// Get all categories
export const useCategories = () => {
  return useQuery({
    queryKey: queryKeys.categories.all,
    queryFn: categoryService.getAll,
    staleTime: 30 * 1000, // 30 seconds
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors, but retry on database connection errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false
      }
      // Retry on network/database errors
      return failureCount < 3
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
}

// Get categories for admin
export const useCategoriesForAdmin = () => {
  return useQuery({
    queryKey: queryKeys.categories.admin,
    queryFn: categoryService.getAllForAdmin,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get category by ID
export const useCategory = (id) => {
  return useQuery({
    queryKey: queryKeys.categories.detail(id),
    queryFn: () => categoryService.getById(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
  })
}

// Create category mutation
export const useCreateCategory = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: categoryService.create,
    onSuccess: (data) => {
      // Invalidate and refetch categories
      queryClient.invalidateQueries({ queryKey: queryKeys.categories.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.categories.admin })
      
      toast.success('Category created successfully!')
      return data
    },
    onError: (error) => {
      console.error('Create category error:', error)
      toast.error(error.message || 'Failed to create category')
    },
  })
}

// Update category mutation
export const useUpdateCategory = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, categoryData }) => categoryService.update(id, categoryData),
    onSuccess: (data, variables) => {
      // Invalidate and refetch categories
      queryClient.invalidateQueries({ queryKey: queryKeys.categories.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.categories.admin })
      queryClient.invalidateQueries({ queryKey: queryKeys.categories.detail(variables.id) })
      
      toast.success('Category updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update category error:', error)
      toast.error(error.message || 'Failed to update category')
    },
  })
}

// Delete category mutation
export const useDeleteCategory = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: categoryService.delete,
    onSuccess: (data, categoryId) => {
      // Invalidate and refetch categories
      queryClient.invalidateQueries({ queryKey: queryKeys.categories.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.categories.admin })
      
      // Remove specific category from cache
      queryClient.removeQueries({ queryKey: queryKeys.categories.detail(categoryId) })
      
      toast.success('Category deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete category error:', error)
      toast.error(error.message || 'Failed to delete category')
    },
  })
}
