{"version": 3, "sources": ["../../src/utils/coords.ts"], "sourcesContent": ["import type { XYCoord } from '../interfaces.js'\nimport type { State } from '../reducers/dragOffset.js'\n\n/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */\nexport function add(a: XYCoord, b: XYCoord): XYCoord {\n\treturn {\n\t\tx: a.x + b.x,\n\t\ty: a.y + b.y,\n\t}\n}\n\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */\nexport function subtract(a: XYCoord, b: XYCoord): XYCoord {\n\treturn {\n\t\tx: a.x - b.x,\n\t\ty: a.y - b.y,\n\t}\n}\n\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */\nexport function getSourceClientOffset(state: State): XYCoord | null {\n\tconst { clientOffset, initialClientOffset, initialSourceClientOffset } = state\n\tif (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n\t\treturn null\n\t}\n\treturn subtract(\n\t\tadd(clientOffset, initialSourceClientOffset),\n\t\tinitialClientOffset,\n\t)\n}\n\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */\nexport function getDifferenceFromInitialOffset(state: State): XYCoord | null {\n\tconst { clientOffset, initialClientOffset } = state\n\tif (!clientOffset || !initialClientOffset) {\n\t\treturn null\n\t}\n\treturn subtract(clientOffset, initialClientOffset)\n}\n"], "names": ["add", "a", "b", "x", "y", "subtract", "getSourceClientOffset", "state", "clientOffset", "initialClientOffset", "initialSourceClientOffset", "getDifferenceFromInitialOffset"], "mappings": "AAGA;;;;GAIG,CACH,OAAO,SAASA,GAAG,CAACC,CAAU,EAAEC,CAAU,EAAW;IACpD,OAAO;QACNC,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAGD,CAAC,CAACC,CAAC;QACZC,CAAC,EAAEH,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACE,CAAC;KACZ,CAAA;CACD;AAED;;;;GAIG,CACH,OAAO,SAASC,QAAQ,CAACJ,CAAU,EAAEC,CAAU,EAAW;IACzD,OAAO;QACNC,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAGD,CAAC,CAACC,CAAC;QACZC,CAAC,EAAEH,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACE,CAAC;KACZ,CAAA;CACD;AAED;;;;;;;GAOG,CACH,OAAO,SAASE,qBAAqB,CAACC,KAAY,EAAkB;IACnE,MAAM,EAAEC,YAAY,CAAA,EAAEC,mBAAmB,CAAA,EAAEC,yBAAyB,CAAA,EAAE,GAAGH,KAAK;IAC9E,IAAI,CAACC,YAAY,IAAI,CAACC,mBAAmB,IAAI,CAACC,yBAAyB,EAAE;QACxE,OAAO,IAAI,CAAA;KACX;IACD,OAAOL,QAAQ,CACdL,GAAG,CAACQ,YAAY,EAAEE,yBAAyB,CAAC,EAC5CD,mBAAmB,CACnB,CAAA;CACD;AAED;;;;GAIG,CACH,OAAO,SAASE,8BAA8B,CAACJ,KAAY,EAAkB;IAC5E,MAAM,EAAEC,YAAY,CAAA,EAAEC,mBAAmB,CAAA,EAAE,GAAGF,KAAK;IACnD,IAAI,CAACC,YAAY,IAAI,CAACC,mBAAmB,EAAE;QAC1C,OAAO,IAAI,CAAA;KACX;IACD,OAAOJ,QAAQ,CAACG,YAAY,EAAEC,mBAAmB,CAAC,CAAA;CAClD"}