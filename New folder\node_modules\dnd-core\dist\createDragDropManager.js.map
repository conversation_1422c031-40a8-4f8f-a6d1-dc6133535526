{"version": 3, "sources": ["../src/createDragDropManager.ts"], "sourcesContent": ["import type { Store } from 'redux'\nimport { createStore } from 'redux'\n\nimport { DragDropManagerImpl } from './classes/DragDropManagerImpl.js'\nimport { DragDropMonitorImpl } from './classes/DragDropMonitorImpl.js'\nimport { HandlerRegistryImpl } from './classes/HandlerRegistryImpl.js'\nimport type { BackendFactory, DragDropManager } from './interfaces.js'\nimport type { State } from './reducers/index.js'\nimport { reduce } from './reducers/index.js'\n\nexport function createDragDropManager(\n\tbackendFactory: BackendFactory,\n\tglobalContext: unknown = undefined,\n\tbackendOptions: unknown = {},\n\tdebugMode = false,\n): DragDropManager {\n\tconst store = makeStoreInstance(debugMode)\n\tconst monitor = new DragDropMonitorImpl(store, new HandlerRegistryImpl(store))\n\tconst manager = new DragDropManagerImpl(store, monitor)\n\tconst backend = backendFactory(manager, globalContext, backendOptions)\n\tmanager.receiveBackend(backend)\n\treturn manager\n}\n\nfunction makeStoreInstance(debugMode: boolean): Store<State> {\n\t// TODO: if we ever make a react-native version of this,\n\t// we'll need to consider how to pull off dev-tooling\n\tconst reduxDevTools =\n\t\ttypeof window !== 'undefined' &&\n\t\t(window as any).__REDUX_DEVTOOLS_EXTENSION__\n\treturn createStore(\n\t\treduce,\n\t\tdebugMode &&\n\t\t\treduxDevTools &&\n\t\t\treduxDevTools({\n\t\t\t\tname: 'dnd-core',\n\t\t\t\tinstanceId: 'dnd-core',\n\t\t\t}),\n\t)\n}\n"], "names": ["createStore", "DragDropManagerImpl", "DragDropMonitorImpl", "HandlerRegistryImpl", "reduce", "createDragDropManager", "backendFactory", "globalContext", "undefined", "backendOptions", "debugMode", "store", "makeStoreInstance", "monitor", "manager", "backend", "receiveBackend", "reduxDevTools", "window", "__REDUX_DEVTOOLS_EXTENSION__", "name", "instanceId"], "mappings": "AACA,SAASA,WAAW,QAAQ,OAAO,CAAA;AAEnC,SAASC,mBAAmB,QAAQ,kCAAkC,CAAA;AACtE,SAASC,mBAAmB,QAAQ,kCAAkC,CAAA;AACtE,SAASC,mBAAmB,QAAQ,kCAAkC,CAAA;AAGtE,SAASC,MAAM,QAAQ,qBAAqB,CAAA;AAE5C,OAAO,SAASC,qBAAqB,CACpCC,cAA8B,EAC9BC,aAAsB,GAAGC,SAAS,EAClCC,cAAuB,GAAG,EAAE,EAC5BC,SAAS,GAAG,KAAK,EACC;IAClB,MAAMC,KAAK,GAAGC,iBAAiB,CAACF,SAAS,CAAC;IAC1C,MAAMG,OAAO,GAAG,IAAIX,mBAAmB,CAACS,KAAK,EAAE,IAAIR,mBAAmB,CAACQ,KAAK,CAAC,CAAC;IAC9E,MAAMG,OAAO,GAAG,IAAIb,mBAAmB,CAACU,KAAK,EAAEE,OAAO,CAAC;IACvD,MAAME,OAAO,GAAGT,cAAc,CAACQ,OAAO,EAAEP,aAAa,EAAEE,cAAc,CAAC;IACtEK,OAAO,CAACE,cAAc,CAACD,OAAO,CAAC;IAC/B,OAAOD,OAAO,CAAA;CACd;AAED,SAASF,iBAAiB,CAACF,SAAkB,EAAgB;IAC5D,wDAAwD;IACxD,qDAAqD;IACrD,MAAMO,aAAa,GAClB,OAAOC,MAAM,KAAK,WAAW,IAC7B,AAACA,MAAM,CAASC,4BAA4B;IAC7C,OAAOnB,WAAW,CACjBI,MAAM,EACNM,SAAS,IACRO,aAAa,IACbA,aAAa,CAAC;QACbG,IAAI,EAAE,UAAU;QAChBC,UAAU,EAAE,UAAU;KACtB,CAAC,CACH,CAAA;CACD"}