{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["'use client'\n\nimport * as Devtools from './ReactQueryDevtools'\nimport * as DevtoolsPanel from './ReactQueryDevtoolsPanel'\n\nexport const ReactQueryDevtools: (typeof Devtools)['ReactQueryDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : Devtools.ReactQueryDevtools\n\nexport const ReactQueryDevtoolsPanel: (typeof DevtoolsPanel)['ReactQueryDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : DevtoolsPanel.ReactQueryDevtoolsPanel\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,4BAAAA;AAAA,EAAA,+BAAAC;AAAA;AAAA;AAEA,eAA0B;AAC1B,oBAA+B;AAExB,IAAMD,sBACX,QAAQ,IAAI,aAAa,gBACrB,WAAY;AACV,SAAO;AACT,IACS;AAER,IAAMC,2BACX,QAAQ,IAAI,aAAa,gBACrB,WAAY;AACV,SAAO;AACT,IACc;", "names": ["ReactQueryDevtools", "ReactQueryDevtoolsPanel"]}