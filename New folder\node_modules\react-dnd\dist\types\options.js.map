{"version": 3, "sources": ["../../src/types/options.ts"], "sourcesContent": ["export interface DragSourceOptions {\n\t/**\n\t * Optional. A string. By default, 'move'. In the browsers that support this feature, specifying 'copy'\n\t * shows a special “copying” cursor, while 'move' corresponds to the “move” cursor. You might want to use\n\t * this option to provide a hint to the user about whether an action is destructive.\n\t */\n\tdropEffect?: string\n}\n\nexport interface DragPreviewOptions {\n\t/**\n\t * Optional. A boolean. By default, false. If true, the component will learn that it is being dragged immediately as the drag\n\t * starts instead of the next tick. This means that the screenshotting would occur with monitor.isDragging() already being true,\n\t * and if you apply any styling like a decreased opacity to the dragged element, this styling will also be reflected on the\n\t * screenshot. This is rarely desirable, so false is a sensible default. However, you might want to set it to true in rare cases,\n\t * such as if you want to make the custom drag layers work in IE and you need to hide the original element without resorting to\n\t * an empty drag preview which IE doesn't support.\n\t */\n\tcaptureDraggingState?: boolean\n\n\t/**\n\t * Optional. A number between 0 and 1. By default, 0.5. Specifies how the offset relative to the drag source node is translated\n\t * into the horizontal offset of the drag preview when their sizes don't match. 0 means “dock the preview to the left”, 0.5 means\n\t * “interpolate linearly” and 1 means “dock the preview to the right”.\n\t */\n\tanchorX?: number\n\n\t/**\n\t * Optional. A number between 0 and 1. By default, 0.5. Specifies how the offset relative to the drag source node is translated into\n\t * the vertical offset of the drag preview when their sizes don't match. 0 means “dock the preview to the top, 0.5 means “interpolate\n\t * linearly” and 1 means “dock the preview to the bottom.\n\t */\n\tanchorY?: number\n\n\t/**\n\t * Optional. A number or null if not needed. By default, null. Specifies the vertical offset between the cursor and the drag preview\n\t * element. If offsetX has a value, anchorX won't be used.\n\t */\n\toffsetX?: number\n\n\t/**\n\t *  Optional. A number or null if not needed. By default, null. Specifies the vertical offset between the cursor and the drag\n\t *  preview element. If offsetY has a value, anchorY won't be used.\n\t */\n\toffsetY?: number\n}\n\nexport type DropTargetOptions = any\n"], "names": [], "mappings": "AAAA,WA+CmC"}