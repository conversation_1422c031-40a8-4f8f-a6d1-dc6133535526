import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { userService } from '../services/api'
import { queryKeys } from '../config/reactQuery'
import toast from 'react-hot-toast'

// Get user profile
export const useUserProfile = () => {
  return useQuery({
    queryKey: queryKeys.users.profile,
    queryFn: userService.getProfile,
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!localStorage.getItem('token'),
  })
}

// Get all users (admin)
export const useUsers = () => {
  return useQuery({
    queryKey: queryKeys.users.all,
    queryFn: userService.getAllUsers,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get user by ID
export const useUser = (id) => {
  return useQuery({
    queryKey: queryKeys.users.detail(id),
    queryFn: () => userService.getUserById(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
  })
}

// Get dashboard statistics
export const useDashboardStatistics = () => {
  return useQuery({
    queryKey: queryKeys.users.dashboard.statistics,
    queryFn: userService.getDashboardStatistics,
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!localStorage.getItem('token'),
  })
}

// Get dashboard enrollments
export const useDashboardEnrollments = () => {
  return useQuery({
    queryKey: queryKeys.users.dashboard.enrollments,
    queryFn: userService.getDashboardEnrollments,
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!localStorage.getItem('token'),
  })
}

// Get dashboard quiz attempts
export const useDashboardQuizAttempts = () => {
  return useQuery({
    queryKey: queryKeys.users.dashboard.quizAttempts,
    queryFn: userService.getDashboardQuizAttempts,
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!localStorage.getItem('token'),
  })
}

// Get banned accounts
export const useBannedAccounts = () => {
  return useQuery({
    queryKey: queryKeys.users.banned,
    queryFn: userService.getBannedAccounts,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Update profile mutation
export const useUpdateProfile = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: userService.updateProfile,
    onSuccess: (data) => {
      // Update profile cache
      queryClient.setQueryData(queryKeys.users.profile, data)
      
      // Invalidate auth user cache
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user })
      
      toast.success('Profile updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update profile error:', error)
      toast.error(error.message || 'Failed to update profile')
    },
  })
}

// Create user mutation (admin)
export const useCreateUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: userService.createUser,
    onSuccess: (data) => {
      // Invalidate users list
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all })
      
      toast.success('User created successfully!')
      return data
    },
    onError: (error) => {
      console.error('Create user error:', error)
      toast.error(error.message || 'Failed to create user')
    },
  })
}

// Update user mutation (admin)
export const useUpdateUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, userData }) => userService.updateUser(id, userData),
    onSuccess: (data, variables) => {
      // Invalidate users list and specific user
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.detail(variables.id) })
      
      toast.success('User updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update user error:', error)
      toast.error(error.message || 'Failed to update user')
    },
  })
}

// Delete user mutation (admin)
export const useDeleteUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: userService.deleteUser,
    onSuccess: (data, userId) => {
      // Invalidate users list
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all })
      
      // Remove specific user from cache
      queryClient.removeQueries({ queryKey: queryKeys.users.detail(userId) })
      
      toast.success('User deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete user error:', error)
      toast.error(error.message || 'Failed to delete user')
    },
  })
}

// Recalculate progress mutation
export const useRecalculateProgress = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: userService.recalculateProgress,
    onSuccess: (data, courseId) => {
      // Invalidate dashboard statistics
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.statistics })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.enrollments })
      
      toast.success('Progress recalculated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Recalculate progress error:', error)
      toast.error(error.message || 'Failed to recalculate progress')
    },
  })
}

// Unban user mutation
export const useUnbanUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: userService.unbanUser,
    onSuccess: (data) => {
      // Invalidate banned accounts and users list
      queryClient.invalidateQueries({ queryKey: queryKeys.users.banned })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all })
      
      toast.success('User unbanned successfully!')
      return data
    },
    onError: (error) => {
      console.error('Unban user error:', error)
      toast.error(error.message || 'Failed to unban user')
    },
  })
}

// Delete ban record mutation
export const useDeleteBanRecord = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: userService.deleteBanRecord,
    onSuccess: (data) => {
      // Invalidate banned accounts
      queryClient.invalidateQueries({ queryKey: queryKeys.users.banned })
      
      toast.success('Ban record deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete ban record error:', error)
      toast.error(error.message || 'Failed to delete ban record')
    },
  })
}
