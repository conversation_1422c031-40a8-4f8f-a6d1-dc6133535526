{"version": 3, "sources": ["../src/BrowserDetector.ts"], "sourcesContent": ["import { memoize } from './utils/js_utils.js'\n\ndeclare global {\n\tinterface Window extends HTMLElement {\n\t\tsafari: any\n\t}\n}\n\nexport type Predicate = () => boolean\nexport const isFirefox: Predicate = memoize(() =>\n\t/firefox/i.test(navigator.userAgent),\n)\nexport const isSafari: Predicate = memoize(() => Boolean(window.safari))\n"], "names": ["memoize", "isFirefox", "test", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "window", "safari"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB,CAAA;AAS7C,OAAO,MAAMC,SAAS,GAAcD,OAAO,CAAC,IAC3C,WAAWE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;AAAA,CACpC,CAAA;AACD,OAAO,MAAMC,QAAQ,GAAcL,OAAO,CAAC,IAAMM,OAAO,CAACC,MAAM,CAACC,MAAM,CAAC;AAAA,CAAC,CAAA"}