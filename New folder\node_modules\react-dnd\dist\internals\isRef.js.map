{"version": 3, "sources": ["../../src/internals/isRef.ts"], "sourcesContent": ["export interface Ref<T> {\n\tcurrent: T\n}\n\nexport function isRef(obj: unknown): boolean {\n\treturn (\n\t\t// eslint-disable-next-line no-prototype-builtins\n\t\tobj !== null &&\n\t\ttypeof obj === 'object' &&\n\t\tObject.prototype.hasOwnProperty.call(obj, 'current')\n\t)\n}\n"], "names": ["isRef", "obj", "Object", "prototype", "hasOwnProperty", "call"], "mappings": "AAIA,OAAO,SAASA,KAAK,CAACC,GAAY,EAAW;IAC5C,OACC,iDAAiD;IACjDA,GAAG,KAAK,IAAI,IACZ,OAAOA,GAAG,KAAK,QAAQ,IACvBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,SAAS,CAAC,EACpD;CACD"}