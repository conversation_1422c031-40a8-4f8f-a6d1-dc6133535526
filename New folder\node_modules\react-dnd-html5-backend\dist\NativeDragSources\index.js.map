{"version": 3, "sources": ["../../src/NativeDragSources/index.ts"], "sourcesContent": ["import { NativeDragSource } from './NativeDragSource.js'\nimport { nativeTypesConfig } from './nativeTypesConfig.js'\n\nexport function createNativeDragSource(\n\ttype: string,\n\tdataTransfer?: DataTransfer,\n): NativeDragSource {\n\tconst config = nativeTypesConfig[type]\n\tif (!config) {\n\t\tthrow new Error(`native type ${type} has no configuration`)\n\t}\n\tconst result = new NativeDragSource(config)\n\tresult.loadDataTransfer(dataTransfer)\n\treturn result\n}\n\nexport function matchNativeItemType(\n\tdataTransfer: DataTransfer | null,\n): string | null {\n\tif (!dataTransfer) {\n\t\treturn null\n\t}\n\n\tconst dataTransferTypes = Array.prototype.slice.call(dataTransfer.types || [])\n\treturn (\n\t\tObject.keys(nativeTypesConfig).filter((nativeItemType) => {\n\t\t\tconst typeConfig = nativeTypesConfig[nativeItemType]\n\t\t\tif (!typeConfig?.matchesTypes) {\n\t\t\t\treturn false\n\t\t\t}\n\t\t\treturn typeConfig.matchesTypes.some(\n\t\t\t\t(t) => dataTransferTypes.indexOf(t) > -1,\n\t\t\t)\n\t\t})[0] || null\n\t)\n}\n"], "names": ["NativeDragSource", "nativeTypesConfig", "createNativeDragSource", "type", "dataTransfer", "config", "Error", "result", "loadDataTransfer", "matchNativeItemType", "dataTransferTypes", "Array", "prototype", "slice", "call", "types", "Object", "keys", "filter", "nativeItemType", "typeConfig", "matchesTypes", "some", "t", "indexOf"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAuB,CAAA;AACxD,SAASC,iBAAiB,QAAQ,wBAAwB,CAAA;AAE1D,OAAO,SAASC,sBAAsB,CACrCC,IAAY,EACZC,YAA2B,EACR;IACnB,MAAMC,MAAM,GAAGJ,iBAAiB,CAACE,IAAI,CAAC;IACtC,IAAI,CAACE,MAAM,EAAE;QACZ,MAAM,IAAIC,KAAK,CAAC,CAAC,YAAY,EAAEH,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAA;KAC3D;IACD,MAAMI,MAAM,GAAG,IAAIP,gBAAgB,CAACK,MAAM,CAAC;IAC3CE,MAAM,CAACC,gBAAgB,CAACJ,YAAY,CAAC;IACrC,OAAOG,MAAM,CAAA;CACb;AAED,OAAO,SAASE,mBAAmB,CAClCL,YAAiC,EACjB;IAChB,IAAI,CAACA,YAAY,EAAE;QAClB,OAAO,IAAI,CAAA;KACX;IAED,MAAMM,iBAAiB,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACV,YAAY,CAACW,KAAK,IAAI,EAAE,CAAC;IAC9E,OACCC,MAAM,CAACC,IAAI,CAAChB,iBAAiB,CAAC,CAACiB,MAAM,CAAC,CAACC,cAAc,GAAK;QACzD,MAAMC,UAAU,GAAGnB,iBAAiB,CAACkB,cAAc,CAAC;QACpD,IAAI,CAACC,CAAAA,UAAU,aAAVA,UAAU,WAAc,GAAxBA,KAAAA,CAAwB,GAAxBA,UAAU,CAAEC,YAAY,CAAA,EAAE;YAC9B,OAAO,KAAK,CAAA;SACZ;QACD,OAAOD,UAAU,CAACC,YAAY,CAACC,IAAI,CAClC,CAACC,CAAC,GAAKb,iBAAiB,CAACc,OAAO,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;QAAA,CACxC,CAAA;KACD,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CACb;CACD"}