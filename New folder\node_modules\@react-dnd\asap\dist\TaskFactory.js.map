{"version": 3, "sources": ["../src/TaskFactory.ts"], "sourcesContent": ["import { RawTask } from './RawTask.js'\nimport type { Task } from './types.js'\n\nexport class TaskFactory {\n\tprivate freeTasks: RawTask[] = []\n\n\tpublic constructor(private onError: (err: any) => void) {}\n\n\tpublic create(task: () => void): Task {\n\t\tconst tasks = this.freeTasks\n\t\tconst t = tasks.length\n\t\t\t? (tasks.pop() as RawTask)\n\t\t\t: new RawTask(this.onError, (t) => (tasks[tasks.length] = t))\n\t\tt.task = task\n\t\treturn t\n\t}\n}\n"], "names": ["RawTask", "TaskFactory", "create", "task", "tasks", "freeTasks", "t", "length", "pop", "onError"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc,CAAA;AAGtC,OAAO,MAAMC,WAAW;IAKvB,AAAOC,MAAM,CAACC,IAAgB,EAAQ;QACrC,MAAMC,KAAK,GAAG,IAAI,CAACC,SAAS;QAC5B,MAAMC,EAAC,GAAGF,KAAK,CAACG,MAAM,GAClBH,KAAK,CAACI,GAAG,EAAE,GACZ,IAAIR,OAAO,CAAC,IAAI,CAACS,OAAO,EAAE,CAACH,CAAC,GAAMF,KAAK,CAACA,KAAK,CAACG,MAAM,CAAC,GAAGD,CAAC;QAAC,CAAC;QAC9DA,EAAC,CAACH,IAAI,GAAGA,IAAI;QACb,OAAOG,EAAC,CAAA;KACR;IATD,YAA2BG,OAA2B,CAAE;aAA7BA,OAA2B,GAA3BA,OAA2B;aAF9CJ,SAAS,GAAc,EAAE;KAEyB;CAU1D"}