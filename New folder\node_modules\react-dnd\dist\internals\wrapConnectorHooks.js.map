{"version": 3, "sources": ["../../src/internals/wrapConnectorHooks.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { ReactElement } from 'react'\nimport { cloneElement, isValidElement } from 'react'\n\nfunction throwIfCompositeComponentElement(element: ReactElement<any>) {\n\t// Custom components can no longer be wrapped directly in React DnD 2.0\n\t// so that we don't need to depend on findDOMNode() from react-dom.\n\tif (typeof element.type === 'string') {\n\t\treturn\n\t}\n\n\tconst displayName =\n\t\t(element.type as any).displayName || element.type.name || 'the component'\n\n\tthrow new Error(\n\t\t'Only native element nodes can now be passed to React DnD connectors.' +\n\t\t\t`You can either wrap ${displayName} into a <div>, or turn it into a ` +\n\t\t\t'drag source or a drop target itself.',\n\t)\n}\n\nfunction wrapHookToRecognizeElement(hook: (node: any, options: any) => void) {\n\treturn (elementOrNode = null, options = null) => {\n\t\t// When passed a node, call the hook straight away.\n\t\tif (!isValidElement(elementOrNode)) {\n\t\t\tconst node = elementOrNode\n\t\t\thook(node, options)\n\t\t\t// return the node so it can be chained (e.g. when within callback refs\n\t\t\t// <div ref={node => connectDragSource(connectDropTarget(node))}/>\n\t\t\treturn node\n\t\t}\n\n\t\t// If passed a ReactElement, clone it and attach this function as a ref.\n\t\t// This helps us achieve a neat API where user doesn't even know that refs\n\t\t// are being used under the hood.\n\t\tconst element: ReactElement | null = elementOrNode\n\t\tthrowIfCompositeComponentElement(element as any)\n\n\t\t// When no options are passed, use the hook directly\n\t\tconst ref = options ? (node: Element) => hook(node, options) : hook\n\t\treturn cloneWithRef(element, ref)\n\t}\n}\n\nexport function wrapConnectorHooks(hooks: any) {\n\tconst wrappedHooks: any = {}\n\n\tObject.keys(hooks).forEach((key) => {\n\t\tconst hook = hooks[key]\n\n\t\t// ref objects should be passed straight through without wrapping\n\t\tif (key.endsWith('Ref')) {\n\t\t\twrappedHooks[key] = hooks[key]\n\t\t} else {\n\t\t\tconst wrappedHook = wrapHookToRecognizeElement(hook)\n\t\t\twrappedHooks[key] = () => wrappedHook\n\t\t}\n\t})\n\n\treturn wrappedHooks\n}\n\nfunction setRef(ref: any, node: any) {\n\tif (typeof ref === 'function') {\n\t\tref(node)\n\t} else {\n\t\tref.current = node\n\t}\n}\n\nfunction cloneWithRef(element: any, newRef: any): ReactElement<any> {\n\tconst previousRef = element.ref\n\tinvariant(\n\t\ttypeof previousRef !== 'string',\n\t\t'Cannot connect React DnD to an element with an existing string ref. ' +\n\t\t\t'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' +\n\t\t\t'Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs',\n\t)\n\n\tif (!previousRef) {\n\t\t// When there is no ref on the element, use the new ref directly\n\t\treturn cloneElement(element, {\n\t\t\tref: newRef,\n\t\t})\n\t} else {\n\t\treturn cloneElement(element, {\n\t\t\tref: (node: any) => {\n\t\t\t\tsetRef(previousRef, node)\n\t\t\t\tsetRef(newRef, node)\n\t\t\t},\n\t\t})\n\t}\n}\n"], "names": ["invariant", "cloneElement", "isValidElement", "throwIfCompositeComponentElement", "element", "type", "displayName", "name", "Error", "wrapHookToRecognizeElement", "hook", "elementOrNode", "options", "node", "ref", "cloneWithRef", "wrapConnectorHooks", "hooks", "<PERSON><PERSON><PERSON>s", "Object", "keys", "for<PERSON>ach", "key", "endsWith", "wrappedHook", "setRef", "current", "newRef", "previousRef"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAEhD,SAASC,YAAY,EAAEC,cAAc,QAAQ,OAAO,CAAA;AAEpD,SAASC,gCAAgC,CAACC,OAA0B,EAAE;IACrE,uEAAuE;IACvE,mEAAmE;IACnE,IAAI,OAAOA,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;QACrC,OAAM;KACN;IAED,MAAMC,WAAW,GAChB,AAACF,OAAO,CAACC,IAAI,CAASC,WAAW,IAAIF,OAAO,CAACC,IAAI,CAACE,IAAI,IAAI,eAAe;IAE1E,MAAM,IAAIC,KAAK,CACd,sEAAsE,GACrE,CAAC,oBAAoB,EAAEF,WAAW,CAAC,iCAAiC,CAAC,GACrE,sCAAsC,CACvC,CAAA;CACD;AAED,SAASG,0BAA0B,CAACC,IAAuC,EAAE;IAC5E,OAAO,CAACC,aAAa,GAAG,IAAI,EAAEC,OAAO,GAAG,IAAI,GAAK;QAChD,mDAAmD;QACnD,IAAI,CAACV,cAAc,CAACS,aAAa,CAAC,EAAE;YACnC,MAAME,IAAI,GAAGF,aAAa;YAC1BD,IAAI,CAACG,IAAI,EAAED,OAAO,CAAC;YACnB,uEAAuE;YACvE,kEAAkE;YAClE,OAAOC,IAAI,CAAA;SACX;QAED,wEAAwE;QACxE,0EAA0E;QAC1E,iCAAiC;QACjC,MAAMT,OAAO,GAAwBO,aAAa;QAClDR,gCAAgC,CAACC,OAAO,CAAQ;QAEhD,oDAAoD;QACpD,MAAMU,GAAG,GAAGF,OAAO,GAAG,CAACC,IAAa,GAAKH,IAAI,CAACG,IAAI,EAAED,OAAO,CAAC;QAAA,GAAGF,IAAI;QACnE,OAAOK,YAAY,CAACX,OAAO,EAAEU,GAAG,CAAC,CAAA;KACjC,CAAA;CACD;AAED,OAAO,SAASE,kBAAkB,CAACC,KAAU,EAAE;IAC9C,MAAMC,YAAY,GAAQ,EAAE;IAE5BC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAAC,CAACC,GAAG,GAAK;QACnC,MAAMZ,IAAI,GAAGO,KAAK,CAACK,GAAG,CAAC;QAEvB,iEAAiE;QACjE,IAAIA,GAAG,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACxBL,YAAY,CAACI,GAAG,CAAC,GAAGL,KAAK,CAACK,GAAG,CAAC;SAC9B,MAAM;YACN,MAAME,WAAW,GAAGf,0BAA0B,CAACC,IAAI,CAAC;YACpDQ,YAAY,CAACI,GAAG,CAAC,GAAG,IAAME,WAAW;YAAA;SACrC;KACD,CAAC;IAEF,OAAON,YAAY,CAAA;CACnB;AAED,SAASO,MAAM,CAACX,GAAQ,EAAED,IAAS,EAAE;IACpC,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;QAC9BA,GAAG,CAACD,IAAI,CAAC;KACT,MAAM;QACNC,GAAG,CAACY,OAAO,GAAGb,IAAI;KAClB;CACD;AAED,SAASE,YAAY,CAACX,OAAY,EAAEuB,MAAW,EAAqB;IACnE,MAAMC,WAAW,GAAGxB,OAAO,CAACU,GAAG;IAC/Bd,SAAS,CACR,OAAO4B,WAAW,KAAK,QAAQ,EAC/B,sEAAsE,GACrE,sFAAsF,GACtF,yEAAyE,CAC1E;IAED,IAAI,CAACA,WAAW,EAAE;QACjB,gEAAgE;QAChE,OAAO3B,YAAY,CAACG,OAAO,EAAE;YAC5BU,GAAG,EAAEa,MAAM;SACX,CAAC,CAAA;KACF,MAAM;QACN,OAAO1B,YAAY,CAACG,OAAO,EAAE;YAC5BU,GAAG,EAAE,CAACD,IAAS,GAAK;gBACnBY,MAAM,CAACG,WAAW,EAAEf,IAAI,CAAC;gBACzBY,MAAM,CAACE,MAAM,EAAEd,IAAI,CAAC;aACpB;SACD,CAAC,CAAA;KACF;CACD"}