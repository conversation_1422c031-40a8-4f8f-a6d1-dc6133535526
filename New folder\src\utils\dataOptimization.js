// Data optimization utilities for reducing bundle size and improving performance

/**
 * Remove unnecessary fields from API responses to reduce data transfer
 */
export const optimizeApiResponse = (data, fields = []) => {
  if (!data) return data
  
  if (Array.isArray(data)) {
    return data.map(item => optimizeApiResponse(item, fields))
  }
  
  if (typeof data === 'object') {
    const optimized = {}
    
    // If specific fields are provided, only include those
    if (fields.length > 0) {
      fields.forEach(field => {
        if (data.hasOwnProperty(field)) {
          optimized[field] = data[field]
        }
      })
      return optimized
    }
    
    // Otherwise, remove common unnecessary fields
    const unnecessaryFields = [
      'created_at',
      'updated_at',
      'deleted_at',
      'internal_notes',
      'debug_info',
      'metadata'
    ]
    
    Object.keys(data).forEach(key => {
      if (!unnecessaryFields.includes(key)) {
        optimized[key] = data[key]
      }
    })
    
    return optimized
  }
  
  return data
}

/**
 * Optimize course data for list views
 */
export const optimizeCourseList = (courses) => {
  const essentialFields = [
    'id',
    'title',
    'description',
    'thumbnail',
    'instructor',
    'price',
    'level',
    'category',
    'duration',
    'student_count',
    'rating',
    'status'
  ]
  
  return optimizeApiResponse(courses, essentialFields)
}

/**
 * Optimize user data for admin lists
 */
export const optimizeUserList = (users) => {
  const essentialFields = [
    'id',
    'name',
    'email',
    'role',
    'status',
    'isActive',
    'last_login',
    'created_at'
  ]
  
  return optimizeApiResponse(users, essentialFields)
}

/**
 * Optimize category data
 */
export const optimizeCategoryList = (categories) => {
  const essentialFields = [
    'id',
    'name',
    'slug',
    'description',
    'image_url',
    'courseCount',
    'status'
  ]
  
  return optimizeApiResponse(categories, essentialFields)
}

/**
 * Optimize quiz data for lists
 */
export const optimizeQuizList = (quizzes) => {
  const essentialFields = [
    'id',
    'title',
    'description',
    'course_id',
    'section_id',
    'duration',
    'question_count',
    'attempts_allowed',
    'status'
  ]
  
  return optimizeApiResponse(quizzes, essentialFields)
}

/**
 * Optimize lesson data for lists
 */
export const optimizeLessonList = (lessons) => {
  const essentialFields = [
    'id',
    'title',
    'description',
    'course_id',
    'section_id',
    'type',
    'duration',
    'order',
    'status',
    'is_completed'
  ]
  
  return optimizeApiResponse(lessons, essentialFields)
}

/**
 * Compress image URLs for thumbnails
 */
export const optimizeImageUrl = (url, width = 300, height = 200, quality = 80) => {
  if (!url) return url
  
  // If using a service like Cloudinary or similar, add optimization parameters
  if (url.includes('cloudinary.com')) {
    return url.replace('/upload/', `/upload/w_${width},h_${height},q_${quality},f_auto/`)
  }
  
  // If using Unsplash, add optimization parameters
  if (url.includes('unsplash.com')) {
    return `${url}?w=${width}&h=${height}&q=${quality}&fit=crop`
  }
  
  // For other services, return as-is
  return url
}

/**
 * Lazy load data with pagination
 */
export const createPaginatedLoader = (loadFunction, pageSize = 20) => {
  let currentPage = 1
  let hasMore = true
  let loading = false
  
  return {
    async loadMore() {
      if (loading || !hasMore) return []
      
      loading = true
      try {
        const data = await loadFunction(currentPage, pageSize)
        
        if (data.length < pageSize) {
          hasMore = false
        }
        
        currentPage++
        return data
      } finally {
        loading = false
      }
    },
    
    reset() {
      currentPage = 1
      hasMore = true
      loading = false
    },
    
    get isLoading() {
      return loading
    },
    
    get hasMoreData() {
      return hasMore
    }
  }
}

/**
 * Debounce function for search inputs
 */
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * Memoize expensive calculations
 */
export const memoize = (fn) => {
  const cache = new Map()
  
  return (...args) => {
    const key = JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key)
    }
    
    const result = fn(...args)
    cache.set(key, result)
    
    // Limit cache size to prevent memory leaks
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value
      cache.delete(firstKey)
    }
    
    return result
  }
}

/**
 * Preload critical resources
 */
export const preloadResource = (url, type = 'fetch') => {
  if (typeof window === 'undefined') return
  
  const link = document.createElement('link')
  link.rel = 'preload'
  link.href = url
  
  switch (type) {
    case 'image':
      link.as = 'image'
      break
    case 'script':
      link.as = 'script'
      break
    case 'style':
      link.as = 'style'
      break
    default:
      link.as = 'fetch'
      link.crossOrigin = 'anonymous'
  }
  
  document.head.appendChild(link)
}

/**
 * Optimize FontAwesome imports by only loading used icons
 */
export const optimizeFontAwesome = () => {
  // This would be used in a build process to tree-shake unused icons
  // For now, it's a placeholder for the concept
  console.log('FontAwesome optimization would happen at build time')
}

export default {
  optimizeApiResponse,
  optimizeCourseList,
  optimizeUserList,
  optimizeCategoryList,
  optimizeQuizList,
  optimizeLessonList,
  optimizeImageUrl,
  createPaginatedLoader,
  debounce,
  memoize,
  preloadResource,
  optimizeFontAwesome
}
