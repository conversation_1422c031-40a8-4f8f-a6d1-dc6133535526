import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { settingsService, siteSettingsService } from '../services/api'
import { queryKeys } from '../config/reactQuery'
import toast from 'react-hot-toast'

// Get homepage settings
export const useHomepageSettings = () => {
  return useQuery({
    queryKey: queryKeys.settings.homepage,
    queryFn: settingsService.getHomepageSettings,
    staleTime: 30 * 1000, // 30 seconds
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors, but retry on database connection errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false
      }
      // Retry on network/database errors
      return failureCount < 3
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
}

// Get watermark settings
export const useWatermarkSettings = () => {
  return useQuery({
    queryKey: queryKeys.settings.watermark,
    queryFn: settingsService.getWatermarkSettings,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get all settings
export const useAllSettings = () => {
  return useQuery({
    queryKey: queryKeys.settings.all,
    queryFn: settingsService.getAllSettings,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get notification settings
export const useNotificationSettings = () => {
  return useQuery({
    queryKey: queryKeys.settings.notifications,
    queryFn: settingsService.getNotificationSettings,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get system settings
export const useSystemSettings = () => {
  return useQuery({
    queryKey: queryKeys.settings.system,
    queryFn: settingsService.getSystemSettings,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get site settings
export const useSiteSettings = () => {
  return useQuery({
    queryKey: queryKeys.settings.site,
    queryFn: siteSettingsService.getSiteSettings,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get currencies
export const useCurrencies = () => {
  return useQuery({
    queryKey: queryKeys.settings.currencies,
    queryFn: siteSettingsService.getCurrencies,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get public settings
export const usePublicSettings = () => {
  return useQuery({
    queryKey: queryKeys.settings.public,
    queryFn: siteSettingsService.getPublicSettings,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Update homepage settings mutation
export const useUpdateHomepageSettings = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: settingsService.updateHomepageSettings,
    onSuccess: (data) => {
      // Update cache
      queryClient.setQueryData(queryKeys.settings.homepage, data)
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.all })
      
      toast.success('Homepage settings updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update homepage settings error:', error)
      toast.error(error.message || 'Failed to update homepage settings')
    },
  })
}

// Update watermark settings mutation
export const useUpdateWatermarkSettings = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: settingsService.updateWatermarkSettings,
    onSuccess: (data) => {
      // Update cache
      queryClient.setQueryData(queryKeys.settings.watermark, data)
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.all })
      
      toast.success('Watermark settings updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update watermark settings error:', error)
      toast.error(error.message || 'Failed to update watermark settings')
    },
  })
}

// Update notification settings mutation
export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: settingsService.updateNotificationSettings,
    onSuccess: (data) => {
      // Update cache
      queryClient.setQueryData(queryKeys.settings.notifications, data)
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.all })
      
      toast.success('Notification settings updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update notification settings error:', error)
      toast.error(error.message || 'Failed to update notification settings')
    },
  })
}

// Update system setting mutation
export const useUpdateSystemSetting = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ key, value }) => settingsService.updateSystemSetting(key, value),
    onSuccess: (data) => {
      // Invalidate system settings
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.system })
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.all })
      
      toast.success('System setting updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update system setting error:', error)
      toast.error(error.message || 'Failed to update system setting')
    },
  })
}

// Update site settings mutation
export const useUpdateSiteSettings = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: siteSettingsService.updateSiteSettings,
    onSuccess: (data) => {
      // Update cache
      queryClient.setQueryData(queryKeys.settings.site, data)
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.public })
      
      toast.success('Site settings updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update site settings error:', error)
      toast.error(error.message || 'Failed to update site settings')
    },
  })
}

// Add currency mutation
export const useAddCurrency = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: siteSettingsService.addCurrency,
    onSuccess: (data) => {
      // Invalidate currencies
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.currencies })
      
      toast.success('Currency added successfully!')
      return data
    },
    onError: (error) => {
      console.error('Add currency error:', error)
      toast.error(error.message || 'Failed to add currency')
    },
  })
}

// Delete currency mutation
export const useDeleteCurrency = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: siteSettingsService.deleteCurrency,
    onSuccess: (data) => {
      // Invalidate currencies
      queryClient.invalidateQueries({ queryKey: queryKeys.settings.currencies })
      
      toast.success('Currency deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete currency error:', error)
      toast.error(error.message || 'Failed to delete currency')
    },
  })
}
