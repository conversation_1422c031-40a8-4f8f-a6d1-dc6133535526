{"version": 3, "sources": ["../src/RawTask.ts"], "sourcesContent": ["// We wrap tasks with recyclable task objects.  A task object implements\n\nimport type { Task, TaskFn } from 'types'\n\n// `call`, just like a function.\nexport class RawTask implements Task {\n\tpublic task: TaskFn | null = null\n\n\tpublic constructor(\n\t\tprivate onError: (err: any) => void,\n\t\tprivate release: (t: RawTask) => void,\n\t) {}\n\n\tpublic call() {\n\t\ttry {\n\t\t\tthis.task && this.task()\n\t\t} catch (error) {\n\t\t\tthis.onError(error)\n\t\t} finally {\n\t\t\tthis.task = null\n\t\t\tthis.release(this)\n\t\t}\n\t}\n}\n"], "names": ["RawTask", "call", "task", "error", "onError", "release"], "mappings": "AAIA,gCAAgC;AAChC,OAAO,MAAMA,OAAO;IAQnB,AAAOC,IAAI,GAAG;QACb,IAAI;YACH,IAAI,CAACC,IAAI,IAAI,IAAI,CAACA,IAAI,EAAE;SACxB,CAAC,OAAOC,KAAK,EAAE;YACf,IAAI,CAACC,OAAO,CAACD,KAAK,CAAC;SACnB,QAAS;YACT,IAAI,CAACD,IAAI,GAAG,IAAI;YAChB,IAAI,CAACG,OAAO,CAAC,IAAI,CAAC;SAClB;KACD;IAdD,YACSD,OAA2B,EAC3BC,OAA6B,CACpC;aAFOD,OAA2B,GAA3BA,OAA2B;aAC3BC,OAA6B,GAA7BA,OAA6B;aAJ/BH,IAAI,GAAkB,IAAI;KAK7B;CAYJ"}