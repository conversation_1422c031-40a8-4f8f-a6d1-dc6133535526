import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { courseService } from '../services/api'
import { queryKeys } from '../config/reactQuery'
import toast from 'react-hot-toast'

// Get all courses
export const useCourses = () => {
  return useQuery({
    queryKey: queryKeys.courses.all,
    queryFn: courseService.getAll,
    staleTime: 30 * 1000, // 30 seconds
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors, but retry on database connection errors
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false
      }
      // Retry on network/database errors
      return failureCount < 3
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
}

// Get courses for management
export const useCoursesForManagement = () => {
  return useQuery({
    queryKey: queryKeys.courses.management,
    queryFn: courseService.getAllForManagement,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get course by ID
export const useCourse = (id) => {
  return useQuery({
    queryKey: queryKeys.courses.detail(id),
    queryFn: () => courseService.getById(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
  })
}

// Get course curriculum
export const useCourseCurriculum = (id) => {
  return useQuery({
    queryKey: queryKeys.courses.curriculum(id),
    queryFn: () => courseService.getCurriculum(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
  })
}

// Create course mutation
export const useCreateCourse = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: courseService.create,
    onSuccess: (data) => {
      // Invalidate and refetch courses
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.management })
      
      toast.success('Course created successfully!')
      return data
    },
    onError: (error) => {
      console.error('Create course error:', error)
      toast.error(error.message || 'Failed to create course')
    },
  })
}

// Update course mutation
export const useUpdateCourse = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, courseData }) => courseService.update(id, courseData),
    onSuccess: (data, variables) => {
      // Invalidate and refetch courses
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.management })
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.detail(variables.id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.curriculum(variables.id) })
      
      toast.success('Course updated successfully!')
      return data
    },
    onError: (error) => {
      console.error('Update course error:', error)
      toast.error(error.message || 'Failed to update course')
    },
  })
}

// Delete course mutation
export const useDeleteCourse = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: courseService.delete,
    onSuccess: (data, courseId) => {
      // Invalidate and refetch courses
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.management })
      
      // Remove specific course from cache
      queryClient.removeQueries({ queryKey: queryKeys.courses.detail(courseId) })
      queryClient.removeQueries({ queryKey: queryKeys.courses.curriculum(courseId) })
      
      toast.success('Course deleted successfully!')
      return data
    },
    onError: (error) => {
      console.error('Delete course error:', error)
      toast.error(error.message || 'Failed to delete course')
    },
  })
}

// Enroll in course mutation
export const useEnrollCourse = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: courseService.enroll,
    onSuccess: (data, courseId) => {
      // Invalidate user dashboard and course data
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.enrollments })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.statistics })
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.detail(courseId) })
      
      toast.success('Successfully enrolled in course!')
      return data
    },
    onError: (error) => {
      console.error('Enroll course error:', error)
      toast.error(error.message || 'Failed to enroll in course')
    },
  })
}

// Unenroll from course mutation
export const useUnenrollCourse = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: courseService.unenroll,
    onSuccess: (data, courseId) => {
      // Invalidate user dashboard and course data
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.enrollments })
      queryClient.invalidateQueries({ queryKey: queryKeys.users.dashboard.statistics })
      queryClient.invalidateQueries({ queryKey: queryKeys.courses.detail(courseId) })

      toast.success('Successfully unenrolled from course!')
      return data
    },
    onError: (error) => {
      console.error('Unenroll course error:', error)
      toast.error(error.message || 'Failed to unenroll from course')
    },
  })
}

// Prefetch course data
export const usePrefetchCourse = () => {
  const queryClient = useQueryClient()

  return (courseId) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.courses.detail(courseId),
      queryFn: () => courseService.getById(courseId),
      staleTime: 30 * 1000, // 30 seconds
    })
  }
}
