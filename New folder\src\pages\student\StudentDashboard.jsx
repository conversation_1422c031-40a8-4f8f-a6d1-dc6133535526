import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faChartLine,
  faGraduationCap,
  faClipboardList,
  faUser,
  faBookOpen,
  faCheckCircle,
  faTimes,
  faTrophy,
  faEye,
  faClock,
  faCalendarAlt,
  faPlay,
  faUsers,
  faStar,
  faWallet,
  faPlus,
  faHistory,
  faQrcode
} from '@fortawesome/free-solid-svg-icons'
import Header from '../../components/common/Header'
import LoadingSpinner from '../../components/common/LoadingSpinner'
import QRCodeScanner from '../../components/payment/QRCodeScanner'
import { useAuth } from '../../context/AuthContext'
import { examService, settingsService } from '../../services/api'
import {
  useDashboardStatistics,
  useDashboardEnrollments,
  useDashboardQuizAttempts
} from '../../hooks/useUsers'
import { useWalletBalance, useWalletTransactions } from '../../hooks/usePayment'
import toast from 'react-hot-toast'

const StudentDashboard = () => {
  const { user } = useAuth()
  const navigate = useNavigate()

  // React Query hooks
  const {
    data: statistics = {
      enrolledCourses: 0,
      completedLessons: 0,
      totalQuizAttempts: 0,
      passedQuizAttempts: 0
    },
    isLoading: statisticsLoading
  } = useDashboardStatistics()

  const {
    data: enrollments = [],
    isLoading: enrollmentsLoading
  } = useDashboardEnrollments()

  const {
    data: quizAttempts = [],
    isLoading: quizAttemptsLoading
  } = useDashboardQuizAttempts()

  const {
    data: walletData,
    isLoading: walletLoading
  } = useWalletBalance()

  const {
    data: walletTransactionsData,
    isLoading: transactionsLoading
  } = useWalletTransactions(1, 20)

  const [activeTab, setActiveTab] = useState('statistics')
  const [selectedAttempt, setSelectedAttempt] = useState(null)
  const [attemptDetails, setAttemptDetails] = useState(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [systemSettings, setSystemSettings] = useState({
    quiz_hide_view_details: false
  })
  const [showQRScanner, setShowQRScanner] = useState(false)

  // Combine data for compatibility
  const dashboardData = {
    statistics,
    enrollments,
    quizAttempts
  }

  const walletBalance = walletData?.balance || 0
  const walletTransactions = walletTransactionsData?.transactions || []

  const loading = statisticsLoading || enrollmentsLoading || quizAttemptsLoading

  useEffect(() => {
    fetchSystemSettings()
  }, [])

  const fetchSystemSettings = async () => {
    try {
      const response = await settingsService.getSystemSettings()
      const parsedSettings = {}

      if (response && Array.isArray(response)) {
        response.forEach(setting => {
          if (setting.setting_key && setting.setting_value) {
            try {
              parsedSettings[setting.setting_key] = JSON.parse(setting.setting_value)
            } catch {
              parsedSettings[setting.setting_key] = setting.setting_value
            }
          }
        })
      }

      setSystemSettings(prev => ({
        ...prev,
        quiz_hide_view_details: parsedSettings.quiz_hide_view_details === true || parsedSettings.quiz_hide_view_details === 'true'
      }))
    } catch (error) {
      console.error('Error fetching system settings:', error)
      // Use default settings if loading fails
    }
  }

  const handleViewAttemptDetails = async (attemptId) => {
    try {
      setSelectedAttempt(attemptId)
      const details = await examService.getAttemptDetails(attemptId)
      setAttemptDetails(details)
      setShowDetailsModal(true)
    } catch (error) {
      console.error('Error fetching attempt details:', error)
      toast.error('Failed to load attempt details')
    }
  }

  const handleRecalculateProgress = async (courseId) => {
    try {
      await userService.recalculateProgress(courseId)
      toast.success('Progress updated successfully!')
      // Refresh dashboard data
      fetchDashboardData()
    } catch (error) {
      console.error('Error recalculating progress:', error)
      toast.error('Failed to update progress')
    }
  }

  const fetchWalletData = async () => {
    if (activeTab !== 'wallet') return

    setWalletLoading(true)
    try {
      const [balanceResponse, transactionsResponse] = await Promise.all([
        paymentService.getWalletBalance(),
        paymentService.getWalletTransactions(1, 20)
      ])

      setWalletBalance(balanceResponse.balance)
      setWalletTransactions(transactionsResponse.transactions || [])
    } catch (error) {
      console.error('Error fetching wallet data:', error)
      toast.error('Failed to load wallet data')
    } finally {
      setWalletLoading(false)
    }
  }

  // Fetch wallet data when wallet tab is selected
  useEffect(() => {
    if (activeTab === 'wallet') {
      fetchWalletData()
    }
  }, [activeTab])

  const handleQRCodeScanned = (result) => {
    // Refresh wallet data if it was a wallet recharge
    if (result.type === 'wallet') {
      fetchWalletData()
    }
    // Refresh dashboard data if it was a course purchase
    if (result.type === 'course') {
      fetchDashboardData()
    }
  }



  const tabs = [
    { id: 'statistics', label: 'Statistics', icon: faChartLine },
    { id: 'courses', label: 'My Courses', icon: faGraduationCap },
    { id: 'attempts', label: 'Test Attempts', icon: faClipboardList },
    { id: 'wallet', label: 'Wallet', icon: faWallet },
    { id: 'account', label: 'Account', icon: faUser }
  ]

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getScoreColor = (score, passed) => {
    if (passed) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBadgeColor = (score, passed) => {
    if (passed) return 'badge-success'
    if (score >= 60) return 'badge-warning'
    return 'badge-danger'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Student Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Welcome back, {user?.first_name || user?.username}! Track your learning progress and achievements.
          </p>
        </motion.div>

        {/* Responsive Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          {/* Mobile Tab Selector */}
          <div className="sm:hidden mb-4">
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className="form-input w-full"
            >
              {tabs.map(tab => (
                <option key={tab.id} value={tab.id}>
                  {tab.label}
                </option>
              ))}
            </select>
          </div>

          {/* Desktop Tabs */}
          <div className="hidden sm:block">
            <nav className="flex space-x-1 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    if (tab.id === 'account') {
                      navigate('/profile')
                    } else {
                      setActiveTab(tab.id)
                    }
                  }}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary-600 text-white'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <FontAwesomeIcon icon={tab.icon} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'statistics' && (
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Statistics Cards */}
                <div className="card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Tests Completed</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {dashboardData.statistics.totalQuizAttempts}
                      </p>
                    </div>
                    <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                      <FontAwesomeIcon icon={faClipboardList} className="text-blue-600 text-xl" />
                    </div>
                  </div>
                </div>

                <div className="card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Tests Passed</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {dashboardData.statistics.passedQuizAttempts}
                      </p>
                    </div>
                    <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                      <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-xl" />
                    </div>
                  </div>
                </div>

                <div className="card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Courses Enrolled</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {dashboardData.statistics.enrolledCourses}
                      </p>
                    </div>
                    <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                      <FontAwesomeIcon icon={faGraduationCap} className="text-purple-600 text-xl" />
                    </div>
                  </div>
                </div>

                <div className="card">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Lessons Completed</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {dashboardData.statistics.completedLessons}
                      </p>
                    </div>
                    <div className="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                      <FontAwesomeIcon icon={faBookOpen} className="text-yellow-600 text-xl" />
                    </div>
                  </div>
                </div>

                {/* Success Rate Card */}
                <div className="card md:col-span-2 lg:col-span-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Performance Overview
                  </h3>
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {dashboardData.statistics.totalQuizAttempts > 0
                          ? Math.round((dashboardData.statistics.passedQuizAttempts / dashboardData.statistics.totalQuizAttempts) * 100)
                          : 0}%
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Success Rate</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {(dashboardData.enrollments || []).filter(course => course.completed_at).length}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Completed Courses</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600 mb-1">
                        {(dashboardData.enrollments || []).length - (dashboardData.enrollments || []).filter(course => course.completed_at).length}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">In Progress</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'courses' && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                  My Enrolled Courses
                </h2>
                {(dashboardData.enrollments || []).length > 0 ? (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {(dashboardData.enrollments || []).map((course, index) => (
                      <motion.div
                        key={course.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * index }}
                        className="card hover:shadow-lg transition-shadow"
                      >
                        {/* Course Image */}
                        <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 overflow-hidden relative">
                          {course.thumbnail_url ? (
                            <img
                              src={course.thumbnail_url}
                              alt={course.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <FontAwesomeIcon
                                icon={faGraduationCap}
                                className="text-4xl text-gray-400"
                              />
                            </div>
                          )}
                          {course.completed_at && (
                            <div className="absolute top-2 right-2">
                              <span className="badge badge-success flex items-center space-x-1">
                                <FontAwesomeIcon icon={faTrophy} />
                                <span>Completed</span>
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Course Info */}
                        <div className="mb-4">
                          <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                            {course.title}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                            {course.short_description}
                          </p>

                          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
                            <div className="flex items-center space-x-1">
                              <FontAwesomeIcon icon={faCalendarAlt} />
                              <span>Enrolled {formatDate(course.enrolled_at)}</span>
                            </div>
                            {course.duration && (
                              <div className="flex items-center space-x-1">
                                <FontAwesomeIcon icon={faClock} />
                                <span>{course.duration}h</span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-between">
                            <span className={`badge ${
                              course.level === 'beginner' ? 'badge-success' :
                              course.level === 'intermediate' ? 'badge-warning' :
                              course.level === 'advanced' ? 'badge-danger' : 'badge-secondary'
                            }`}>
                              {course.level?.charAt(0).toUpperCase() + course.level?.slice(1)}
                            </span>
                            {course.instructor_name && (
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                by {course.instructor_name}
                              </p>
                            )}
                          </div>

                          {/* Progress Bar */}
                          <div className="mt-3">
                            <div className="flex items-center justify-between text-sm mb-1">
                              <span className="text-gray-600 dark:text-gray-400">Progress</span>
                              <span className="text-gray-900 dark:text-white font-medium">
                                {Math.round(course.progress || 0)}%
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${course.progress || 0}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="pt-4 border-t border-gray-200 dark:border-gray-700 space-y-2">
                          <Link
                            to={`/courses/${course.id}`}
                            className="w-full btn-primary flex items-center justify-center space-x-2"
                          >
                            <FontAwesomeIcon icon={faPlay} />
                            <span>Continue Learning</span>
                          </Link>
                          {course.progress < 100 && (
                            <button
                              onClick={() => handleRecalculateProgress(course.id)}
                              className="w-full btn-secondary text-xs flex items-center justify-center space-x-1"
                              title="Refresh progress calculation"
                            >
                              <FontAwesomeIcon icon={faCheckCircle} />
                              <span>Refresh Progress</span>
                            </button>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FontAwesomeIcon
                      icon={faGraduationCap}
                      className="text-6xl text-gray-400 mb-4"
                    />
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      No courses enrolled yet
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      Start your learning journey by enrolling in a course.
                    </p>
                    <Link to="/courses" className="btn-primary">
                      Browse Courses
                    </Link>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'attempts' && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                  Test Attempts History
                </h2>
                {(dashboardData.quizAttempts || []).length > 0 ? (
                  <div className="space-y-4">
                    {(dashboardData.quizAttempts || []).map((attempt, index) => (
                      <motion.div
                        key={attempt.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * index }}
                        className="card"
                      >
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-2">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {attempt.quiz_title}
                              </h3>
                              <span className={`badge ${getScoreBadgeColor(attempt.score, attempt.passed)}`}>
                                {attempt.passed ? 'Passed' : 'Failed'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                              Course: {attempt.course_title}
                            </p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                              <div className="flex items-center space-x-1">
                                <FontAwesomeIcon icon={faCalendarAlt} />
                                <span>{formatDate(attempt.completed_at)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <FontAwesomeIcon icon={faCheckCircle} />
                                <span>{attempt.correctAnswers}/{attempt.totalQuestions} correct</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-4">
                            <div className="text-center">
                              <div className={`text-2xl font-bold ${getScoreColor(attempt.score, attempt.passed)}`}>
                                {Math.round(attempt.score)}%
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                Score
                              </div>
                            </div>
                            {!systemSettings.quiz_hide_view_details && (
                              <button
                                onClick={() => handleViewAttemptDetails(attempt.id)}
                                className="btn-secondary flex items-center space-x-2"
                              >
                                <FontAwesomeIcon icon={faEye} />
                                <span>Details</span>
                              </button>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FontAwesomeIcon
                      icon={faClipboardList}
                      className="text-6xl text-gray-400 mb-4"
                    />
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      No test attempts yet
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      Take your first quiz to see your results here.
                    </p>
                    <Link to="/courses" className="btn-primary">
                      Browse Courses
                    </Link>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'wallet' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    My Wallet
                  </h2>
                  <button
                    onClick={fetchWalletData}
                    className="btn-secondary text-sm"
                    disabled={walletLoading}
                  >
                    <FontAwesomeIcon icon={faHistory} className="mr-2" />
                    Refresh
                  </button>
                </div>

                {/* Wallet Balance Card */}
                <div className="card mb-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                      ${walletBalance.toFixed(2)}
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      Current Balance
                    </p>
                    <button
                      onClick={() => setShowQRScanner(true)}
                      className="btn-primary"
                    >
                      <FontAwesomeIcon icon={faQrcode} className="mr-2" />
                      Recharge Wallet
                    </button>
                  </div>
                </div>

                {/* Transaction History */}
                <div className="card">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Transaction History
                  </h3>

                  {walletLoading ? (
                    <div className="text-center py-8">
                      <LoadingSpinner />
                    </div>
                  ) : walletTransactions.length > 0 ? (
                    <div className="space-y-3">
                      {walletTransactions.map((transaction, index) => (
                        <motion.div
                          key={transaction.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 * index }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div className="flex items-center space-x-3">
                            <div className={`p-2 rounded-full ${
                              transaction.type === 'credit'
                                ? 'bg-green-100 dark:bg-green-900/20'
                                : 'bg-red-100 dark:bg-red-900/20'
                            }`}>
                              <FontAwesomeIcon
                                icon={transaction.type === 'credit' ? faPlus : faWallet}
                                className={transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'}
                              />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">
                                {transaction.description}
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {formatDate(transaction.created_at)}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`font-semibold ${
                              transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {transaction.type === 'credit' ? '+' : '-'}${transaction.amount}
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Balance: ${transaction.balance_after}
                            </p>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FontAwesomeIcon
                        icon={faWallet}
                        className="text-4xl text-gray-400 mb-4"
                      />
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        No transactions yet
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Your wallet transaction history will appear here.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Attempt Details Modal */}
        <AnimatePresence>
          {showDetailsModal && attemptDetails && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
              onClick={() => setShowDetailsModal(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {attemptDetails.quiz_title}
                      </h2>
                      <p className="text-gray-600 dark:text-gray-400">
                        {attemptDetails.course_title} • {formatDate(attemptDetails.completed_at)}
                      </p>
                    </div>
                    <button
                      onClick={() => setShowDetailsModal(false)}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <FontAwesomeIcon icon={faTimes} className="text-xl" />
                    </button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className={`text-xl font-bold ${getScoreColor(attemptDetails.score, attemptDetails.passed)}`}>
                        {Math.round(attemptDetails.score)}%
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Final Score</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-xl font-bold text-green-600">
                        {attemptDetails.correct_count}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Correct</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-xl font-bold text-red-600">
                        {attemptDetails.total_questions - attemptDetails.correct_count}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Incorrect</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="text-xl font-bold text-blue-600">
                        {attemptDetails.passing_score}%
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">Required</div>
                    </div>
                  </div>
                </div>

                <div className="p-6 overflow-y-auto max-h-[60vh]">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Question Review
                  </h3>
                  <div className="space-y-6">
                    {attemptDetails.questions.map((question, index) => (
                      <div key={question.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            Question {index + 1}
                          </h4>
                          <span className={`badge ${question.is_correct ? 'badge-success' : 'badge-danger'}`}>
                            {question.is_correct ? 'Correct' : 'Incorrect'}
                          </span>
                        </div>

                        <p className="text-gray-700 dark:text-gray-300 mb-4">
                          {question.question_text}
                        </p>

                        {question.question_type === 'multiple_choice' && question.options && (
                          <div className="space-y-2 mb-4">
                            {question.options.map((option, optIndex) => (
                              <div
                                key={optIndex}
                                className={`p-2 rounded border ${
                                  option === question.correct_answer
                                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                                    : option === question.user_answer && !question.is_correct
                                    ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                                    : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'
                                }`}
                              >
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm font-medium">
                                    {String.fromCharCode(65 + optIndex)}.
                                  </span>
                                  <span className="text-sm">{option}</span>
                                  {option === question.correct_answer && (
                                    <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 ml-auto" />
                                  )}
                                  {option === question.user_answer && !question.is_correct && (
                                    <FontAwesomeIcon icon={faTimes} className="text-red-600 ml-auto" />
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        <div className="grid md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-600 dark:text-gray-400">Your Answer: </span>
                            <span className={question.is_correct ? 'text-green-600' : 'text-red-600'}>
                              {question.user_answer || 'No answer'}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600 dark:text-gray-400">Correct Answer: </span>
                            <span className="text-green-600">{question.correct_answer}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* QR Code Scanner */}
        <QRCodeScanner
          isOpen={showQRScanner}
          onClose={() => setShowQRScanner(false)}
          onCodeScanned={handleQRCodeScanned}
        />
      </div>
    </div>
  )
}

export default StudentDashboard
