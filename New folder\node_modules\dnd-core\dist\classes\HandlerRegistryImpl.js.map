{"version": 3, "sources": ["../../src/classes/HandlerRegistryImpl.ts"], "sourcesContent": ["import { asap } from '@react-dnd/asap'\nimport { invariant } from '@react-dnd/invariant'\nimport type { Store } from 'redux'\n\nimport {\n\taddSource,\n\taddTarget,\n\tremoveSource,\n\tremoveTarget,\n} from '../actions/registry.js'\nimport {\n\tvalidateSourceContract,\n\tvalidateTargetContract,\n\tvalidateType,\n} from '../contracts.js'\nimport type {\n\tDragSource,\n\tDropTarget,\n\tHandlerRegistry,\n\tIdentifier,\n\tSourceType,\n\tTargetType,\n} from '../interfaces.js'\nimport { HandlerRole } from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport { getNextUniqueId } from '../utils/getNextUniqueId.js'\n\nfunction getNextHandlerId(role: HandlerRole): string {\n\tconst id = getNextUniqueId().toString()\n\tswitch (role) {\n\t\tcase HandlerRole.SOURCE:\n\t\t\treturn `S${id}`\n\t\tcase HandlerRole.TARGET:\n\t\t\treturn `T${id}`\n\t\tdefault:\n\t\t\tthrow new Error(`Unknown Handler Role: ${role}`)\n\t}\n}\n\nfunction parseRoleFromHandlerId(handlerId: string) {\n\tswitch (handlerId[0]) {\n\t\tcase 'S':\n\t\t\treturn HandlerRole.SOURCE\n\t\tcase 'T':\n\t\t\treturn HandlerRole.TARGET\n\t\tdefault:\n\t\t\tthrow new Error(`Cannot parse handler ID: ${handlerId}`)\n\t}\n}\n\nfunction mapContainsValue<T>(map: Map<string, T>, searchValue: T) {\n\tconst entries = map.entries()\n\tlet isDone = false\n\tdo {\n\t\tconst {\n\t\t\tdone,\n\t\t\tvalue: [, value],\n\t\t} = entries.next()\n\t\tif (value === searchValue) {\n\t\t\treturn true\n\t\t}\n\t\tisDone = !!done\n\t} while (!isDone)\n\treturn false\n}\n\nexport class HandlerRegistryImpl implements HandlerRegistry {\n\tprivate types: Map<string, SourceType | TargetType> = new Map()\n\tprivate dragSources: Map<string, DragSource> = new Map()\n\tprivate dropTargets: Map<string, DropTarget> = new Map()\n\tprivate pinnedSourceId: string | null = null\n\tprivate pinnedSource: any = null\n\tprivate store: Store<State>\n\n\tpublic constructor(store: Store<State>) {\n\t\tthis.store = store\n\t}\n\n\tpublic addSource(type: SourceType, source: DragSource): string {\n\t\tvalidateType(type)\n\t\tvalidateSourceContract(source)\n\n\t\tconst sourceId = this.addHandler(HandlerRole.SOURCE, type, source)\n\t\tthis.store.dispatch(addSource(sourceId))\n\t\treturn sourceId\n\t}\n\n\tpublic addTarget(type: TargetType, target: DropTarget): string {\n\t\tvalidateType(type, true)\n\t\tvalidateTargetContract(target)\n\n\t\tconst targetId = this.addHandler(HandlerRole.TARGET, type, target)\n\t\tthis.store.dispatch(addTarget(targetId))\n\t\treturn targetId\n\t}\n\n\tpublic containsHandler(handler: DragSource | DropTarget): boolean {\n\t\treturn (\n\t\t\tmapContainsValue(this.dragSources, handler) ||\n\t\t\tmapContainsValue(this.dropTargets, handler)\n\t\t)\n\t}\n\n\tpublic getSource(sourceId: string, includePinned = false): DragSource {\n\t\tinvariant(this.isSourceId(sourceId), 'Expected a valid source ID.')\n\t\tconst isPinned = includePinned && sourceId === this.pinnedSourceId\n\t\tconst source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId)\n\t\treturn source\n\t}\n\n\tpublic getTarget(targetId: string): DropTarget {\n\t\tinvariant(this.isTargetId(targetId), 'Expected a valid target ID.')\n\t\treturn this.dropTargets.get(targetId) as DropTarget\n\t}\n\n\tpublic getSourceType(sourceId: string): Identifier {\n\t\tinvariant(this.isSourceId(sourceId), 'Expected a valid source ID.')\n\t\treturn this.types.get(sourceId) as Identifier\n\t}\n\n\tpublic getTargetType(targetId: string): Identifier | Identifier[] {\n\t\tinvariant(this.isTargetId(targetId), 'Expected a valid target ID.')\n\t\treturn this.types.get(targetId) as Identifier | Identifier[]\n\t}\n\n\tpublic isSourceId(handlerId: string): boolean {\n\t\tconst role = parseRoleFromHandlerId(handlerId)\n\t\treturn role === HandlerRole.SOURCE\n\t}\n\n\tpublic isTargetId(handlerId: string): boolean {\n\t\tconst role = parseRoleFromHandlerId(handlerId)\n\t\treturn role === HandlerRole.TARGET\n\t}\n\n\tpublic removeSource(sourceId: string): void {\n\t\tinvariant(this.getSource(sourceId), 'Expected an existing source.')\n\t\tthis.store.dispatch(removeSource(sourceId))\n\t\tasap(() => {\n\t\t\tthis.dragSources.delete(sourceId)\n\t\t\tthis.types.delete(sourceId)\n\t\t})\n\t}\n\n\tpublic removeTarget(targetId: string): void {\n\t\tinvariant(this.getTarget(targetId), 'Expected an existing target.')\n\t\tthis.store.dispatch(removeTarget(targetId))\n\t\tthis.dropTargets.delete(targetId)\n\t\tthis.types.delete(targetId)\n\t}\n\n\tpublic pinSource(sourceId: string): void {\n\t\tconst source = this.getSource(sourceId)\n\t\tinvariant(source, 'Expected an existing source.')\n\n\t\tthis.pinnedSourceId = sourceId\n\t\tthis.pinnedSource = source\n\t}\n\n\tpublic unpinSource(): void {\n\t\tinvariant(this.pinnedSource, 'No source is pinned at the time.')\n\n\t\tthis.pinnedSourceId = null\n\t\tthis.pinnedSource = null\n\t}\n\n\tprivate addHandler(\n\t\trole: HandlerRole,\n\t\ttype: SourceType | TargetType,\n\t\thandler: DragSource | DropTarget,\n\t): string {\n\t\tconst id = getNextHandlerId(role)\n\t\tthis.types.set(id, type)\n\t\tif (role === HandlerRole.SOURCE) {\n\t\t\tthis.dragSources.set(id, handler as DragSource)\n\t\t} else if (role === HandlerRole.TARGET) {\n\t\t\tthis.dropTargets.set(id, handler as DropTarget)\n\t\t}\n\t\treturn id\n\t}\n}\n"], "names": ["asap", "invariant", "addSource", "addTarget", "removeSource", "remove<PERSON>arget", "validateSourceContract", "validateTargetContract", "validateType", "HandlerRole", "getNextUniqueId", "getNextHandlerId", "role", "id", "toString", "SOURCE", "TARGET", "Error", "parseRoleFromHandlerId", "handlerId", "mapContainsValue", "map", "searchValue", "entries", "isDone", "done", "value", "next", "HandlerRegistryImpl", "type", "source", "sourceId", "add<PERSON><PERSON><PERSON>", "store", "dispatch", "target", "targetId", "<PERSON><PERSON><PERSON><PERSON>", "handler", "dragSources", "dropTargets", "getSource", "include<PERSON><PERSON>ed", "isSourceId", "isPinned", "pinnedSourceId", "pinnedSource", "get", "get<PERSON><PERSON><PERSON>", "isTargetId", "getSourceType", "types", "getTargetType", "delete", "pinSource", "unpinSource", "set", "Map"], "mappings": "AAAA,SAASA,IAAI,QAAQ,iBAAiB,CAAA;AACtC,SAASC,SAAS,QAAQ,sBAAsB,CAAA;AAGhD,SACCC,SAAS,EACTC,SAAS,EACTC,YAAY,EACZC,YAAY,QACN,wBAAwB,CAAA;AAC/B,SACCC,sBAAsB,EACtBC,sBAAsB,EACtBC,YAAY,QACN,iBAAiB,CAAA;AASxB,SAASC,WAAW,QAAQ,kBAAkB,CAAA;AAE9C,SAASC,eAAe,QAAQ,6BAA6B,CAAA;AAE7D,SAASC,gBAAgB,CAACC,IAAiB,EAAU;IACpD,MAAMC,EAAE,GAAGH,eAAe,EAAE,CAACI,QAAQ,EAAE;IACvC,OAAQF,IAAI;QACX,KAAKH,WAAW,CAACM,MAAM;YACtB,OAAO,CAAC,CAAC,EAAEF,EAAE,CAAC,CAAC,CAAA;QAChB,KAAKJ,WAAW,CAACO,MAAM;YACtB,OAAO,CAAC,CAAC,EAAEH,EAAE,CAAC,CAAC,CAAA;QAChB;YACC,MAAM,IAAII,KAAK,CAAC,CAAC,sBAAsB,EAAEL,IAAI,CAAC,CAAC,CAAC,CAAA;KACjD;CACD;AAED,SAASM,sBAAsB,CAACC,SAAiB,EAAE;IAClD,OAAQA,SAAS,CAAC,CAAC,CAAC;QACnB,KAAK,GAAG;YACP,OAAOV,WAAW,CAACM,MAAM,CAAA;QAC1B,KAAK,GAAG;YACP,OAAON,WAAW,CAACO,MAAM,CAAA;QAC1B;YACC,MAAM,IAAIC,KAAK,CAAC,CAAC,yBAAyB,EAAEE,SAAS,CAAC,CAAC,CAAC,CAAA;KACzD;CACD;AAED,SAASC,gBAAgB,CAAIC,GAAmB,EAAEC,WAAc,EAAE;IACjE,MAAMC,OAAO,GAAGF,GAAG,CAACE,OAAO,EAAE;IAC7B,IAAIC,MAAM,GAAG,KAAK;IAClB,GAAG;QACF,MAAM,EACLC,IAAI,CAAA,EACJC,KAAK,EAAE,GAAGA,KAAK,CAAC,CAAA,IAChB,GAAGH,OAAO,CAACI,IAAI,EAAE;QAClB,IAAID,KAAK,KAAKJ,WAAW,EAAE;YAC1B,OAAO,IAAI,CAAA;SACX;QACDE,MAAM,GAAG,CAAC,CAACC,IAAI;KACf,OAAQ,CAACD,MAAM,CAAC;IACjB,OAAO,KAAK,CAAA;CACZ;AAED,OAAO,MAAMI,mBAAmB;IAY/B,AAAO1B,SAAS,CAAC2B,IAAgB,EAAEC,MAAkB,EAAU;QAC9DtB,YAAY,CAACqB,IAAI,CAAC;QAClBvB,sBAAsB,CAACwB,MAAM,CAAC;QAE9B,MAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACvB,WAAW,CAACM,MAAM,EAAEc,IAAI,EAAEC,MAAM,CAAC;QAClE,IAAI,CAACG,KAAK,CAACC,QAAQ,CAAChC,SAAS,CAAC6B,QAAQ,CAAC,CAAC;QACxC,OAAOA,QAAQ,CAAA;KACf;IAED,AAAO5B,SAAS,CAAC0B,IAAgB,EAAEM,MAAkB,EAAU;QAC9D3B,YAAY,CAACqB,IAAI,EAAE,IAAI,CAAC;QACxBtB,sBAAsB,CAAC4B,MAAM,CAAC;QAE9B,MAAMC,QAAQ,GAAG,IAAI,CAACJ,UAAU,CAACvB,WAAW,CAACO,MAAM,EAAEa,IAAI,EAAEM,MAAM,CAAC;QAClE,IAAI,CAACF,KAAK,CAACC,QAAQ,CAAC/B,SAAS,CAACiC,QAAQ,CAAC,CAAC;QACxC,OAAOA,QAAQ,CAAA;KACf;IAED,AAAOC,eAAe,CAACC,OAAgC,EAAW;QACjE,OACClB,gBAAgB,CAAC,IAAI,CAACmB,WAAW,EAAED,OAAO,CAAC,IAC3ClB,gBAAgB,CAAC,IAAI,CAACoB,WAAW,EAAEF,OAAO,CAAC,CAC3C;KACD;IAED,AAAOG,SAAS,CAACV,QAAgB,EAAEW,aAAa,GAAG,KAAK,EAAc;QACrEzC,SAAS,CAAC,IAAI,CAAC0C,UAAU,CAACZ,QAAQ,CAAC,EAAE,6BAA6B,CAAC;QACnE,MAAMa,QAAQ,GAAGF,aAAa,IAAIX,QAAQ,KAAK,IAAI,CAACc,cAAc;QAClE,MAAMf,MAAM,GAAGc,QAAQ,GAAG,IAAI,CAACE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACQ,GAAG,CAAChB,QAAQ,CAAC;QAC5E,OAAOD,MAAM,CAAA;KACb;IAED,AAAOkB,SAAS,CAACZ,QAAgB,EAAc;QAC9CnC,SAAS,CAAC,IAAI,CAACgD,UAAU,CAACb,QAAQ,CAAC,EAAE,6BAA6B,CAAC;QACnE,OAAO,IAAI,CAACI,WAAW,CAACO,GAAG,CAACX,QAAQ,CAAC,CAAc;KACnD;IAED,AAAOc,aAAa,CAACnB,QAAgB,EAAc;QAClD9B,SAAS,CAAC,IAAI,CAAC0C,UAAU,CAACZ,QAAQ,CAAC,EAAE,6BAA6B,CAAC;QACnE,OAAO,IAAI,CAACoB,KAAK,CAACJ,GAAG,CAAChB,QAAQ,CAAC,CAAc;KAC7C;IAED,AAAOqB,aAAa,CAAChB,QAAgB,EAA6B;QACjEnC,SAAS,CAAC,IAAI,CAACgD,UAAU,CAACb,QAAQ,CAAC,EAAE,6BAA6B,CAAC;QACnE,OAAO,IAAI,CAACe,KAAK,CAACJ,GAAG,CAACX,QAAQ,CAAC,CAA6B;KAC5D;IAED,AAAOO,UAAU,CAACxB,SAAiB,EAAW;QAC7C,MAAMP,IAAI,GAAGM,sBAAsB,CAACC,SAAS,CAAC;QAC9C,OAAOP,IAAI,KAAKH,WAAW,CAACM,MAAM,CAAA;KAClC;IAED,AAAOkC,UAAU,CAAC9B,SAAiB,EAAW;QAC7C,MAAMP,IAAI,GAAGM,sBAAsB,CAACC,SAAS,CAAC;QAC9C,OAAOP,IAAI,KAAKH,WAAW,CAACO,MAAM,CAAA;KAClC;IAED,AAAOZ,YAAY,CAAC2B,QAAgB,EAAQ;QAC3C9B,SAAS,CAAC,IAAI,CAACwC,SAAS,CAACV,QAAQ,CAAC,EAAE,8BAA8B,CAAC;QACnE,IAAI,CAACE,KAAK,CAACC,QAAQ,CAAC9B,YAAY,CAAC2B,QAAQ,CAAC,CAAC;QAC3C/B,IAAI,CAAC,IAAM;YACV,IAAI,CAACuC,WAAW,CAACc,MAAM,CAACtB,QAAQ,CAAC;YACjC,IAAI,CAACoB,KAAK,CAACE,MAAM,CAACtB,QAAQ,CAAC;SAC3B,CAAC;KACF;IAED,AAAO1B,YAAY,CAAC+B,QAAgB,EAAQ;QAC3CnC,SAAS,CAAC,IAAI,CAAC+C,SAAS,CAACZ,QAAQ,CAAC,EAAE,8BAA8B,CAAC;QACnE,IAAI,CAACH,KAAK,CAACC,QAAQ,CAAC7B,YAAY,CAAC+B,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAACI,WAAW,CAACa,MAAM,CAACjB,QAAQ,CAAC;QACjC,IAAI,CAACe,KAAK,CAACE,MAAM,CAACjB,QAAQ,CAAC;KAC3B;IAED,AAAOkB,SAAS,CAACvB,QAAgB,EAAQ;QACxC,MAAMD,MAAM,GAAG,IAAI,CAACW,SAAS,CAACV,QAAQ,CAAC;QACvC9B,SAAS,CAAC6B,MAAM,EAAE,8BAA8B,CAAC;QAEjD,IAAI,CAACe,cAAc,GAAGd,QAAQ;QAC9B,IAAI,CAACe,YAAY,GAAGhB,MAAM;KAC1B;IAED,AAAOyB,WAAW,GAAS;QAC1BtD,SAAS,CAAC,IAAI,CAAC6C,YAAY,EAAE,kCAAkC,CAAC;QAEhE,IAAI,CAACD,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,YAAY,GAAG,IAAI;KACxB;IAED,AAAQd,UAAU,CACjBpB,IAAiB,EACjBiB,IAA6B,EAC7BS,OAAgC,EACvB;QACT,MAAMzB,EAAE,GAAGF,gBAAgB,CAACC,IAAI,CAAC;QACjC,IAAI,CAACuC,KAAK,CAACK,GAAG,CAAC3C,EAAE,EAAEgB,IAAI,CAAC;QACxB,IAAIjB,IAAI,KAAKH,WAAW,CAACM,MAAM,EAAE;YAChC,IAAI,CAACwB,WAAW,CAACiB,GAAG,CAAC3C,EAAE,EAAEyB,OAAO,CAAe;SAC/C,MAAM,IAAI1B,IAAI,KAAKH,WAAW,CAACO,MAAM,EAAE;YACvC,IAAI,CAACwB,WAAW,CAACgB,GAAG,CAAC3C,EAAE,EAAEyB,OAAO,CAAe;SAC/C;QACD,OAAOzB,EAAE,CAAA;KACT;IAzGD,YAAmBoB,KAAmB,CAAE;QAPxC,KAAQkB,KAAK,GAAyC,IAAIM,GAAG,EAAE,AAnEhE,CAmEgE;QAC/D,KAAQlB,WAAW,GAA4B,IAAIkB,GAAG,EAAE,AApEzD,CAoEyD;QACxD,KAAQjB,WAAW,GAA4B,IAAIiB,GAAG,EAAE,AArEzD,CAqEyD;QACxD,KAAQZ,cAAc,GAAkB,IAAI,AAtE7C,CAsE6C;QAC5C,KAAQC,YAAY,GAAQ,IAAI,AAvEjC,CAuEiC;QAI/B,IAAI,CAACb,KAAK,GAAGA,KAAK;KAClB;CAwGD"}