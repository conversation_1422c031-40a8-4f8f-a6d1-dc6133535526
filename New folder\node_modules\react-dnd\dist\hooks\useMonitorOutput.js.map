{"version": 3, "sources": ["../../src/hooks/useMonitorOutput.ts"], "sourcesContent": ["import type { Hand<PERSON><PERSON>anager, MonitorEventEmitter } from '../types/index.js'\nimport { useCollector } from './useCollector.js'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js'\n\nexport function useMonitorOutput<Monitor extends HandlerManager, Collected>(\n\tmonitor: Monitor & MonitorEventEmitter,\n\tcollect: (monitor: Monitor) => Collected,\n\tonCollect?: () => void,\n): Collected {\n\tconst [collected, updateCollected] = useCollector(monitor, collect, onCollect)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction subscribeToMonitorStateChange() {\n\t\t\tconst handlerId = monitor.getHandlerId()\n\t\t\tif (handlerId == null) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn monitor.subscribeToStateChange(updateCollected, {\n\t\t\t\thandlerIds: [handlerId],\n\t\t\t})\n\t\t},\n\t\t[monitor, updateCollected],\n\t)\n\n\treturn collected\n}\n"], "names": ["useCollector", "useIsomorphicLayoutEffect", "useMonitorOutput", "monitor", "collect", "onCollect", "collected", "updateCollected", "subscribeToMonitorStateChange", "handlerId", "getHandlerId", "subscribeToStateChange", "handlerIds"], "mappings": "AACA,SAASA,YAAY,QAAQ,mBAAmB,CAAA;AAChD,SAASC,yBAAyB,QAAQ,gCAAgC,CAAA;AAE1E,OAAO,SAASC,gBAAgB,CAC/BC,OAAsC,EACtCC,OAAwC,EACxCC,SAAsB,EACV;IACZ,MAAM,CAACC,SAAS,EAAEC,eAAe,CAAC,GAAGP,YAAY,CAACG,OAAO,EAAEC,OAAO,EAAEC,SAAS,CAAC;IAE9EJ,yBAAyB,CACxB,SAASO,6BAA6B,GAAG;QACxC,MAAMC,SAAS,GAAGN,OAAO,CAACO,YAAY,EAAE;QACxC,IAAID,SAAS,IAAI,IAAI,EAAE;YACtB,OAAM;SACN;QACD,OAAON,OAAO,CAACQ,sBAAsB,CAACJ,eAAe,EAAE;YACtDK,UAAU,EAAE;gBAACH,SAAS;aAAC;SACvB,CAAC,CAAA;KACF,EACD;QAACN,OAAO;QAAEI,eAAe;KAAC,CAC1B;IAED,OAAOD,SAAS,CAAA;CAChB"}