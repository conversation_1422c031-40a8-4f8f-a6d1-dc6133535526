{"name": "educational-platform", "private": true, "version": "1.0.0", "type": "module", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "vite --mode development", "dev:network": "vite --mode development --host", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "preview": "vite preview", "preview:network": "vite preview --host", "deploy": "npm run build:prod && chmod +x deploy.sh && ./deploy.sh", "start:backend": "cd backend && npm start", "dev:backend": "cd backend && npm run dev", "dev:full": "concurrently \"npm run dev:backend\" \"npm run dev\"", "test:api": "node scripts/test-api-connection.cjs", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop educational-platform-backend", "pm2:restart": "pm2 restart educational-platform-backend", "pm2:logs": "pm2 logs educational-platform-backend", "pm2:status": "pm2 status", "setup:env": "node scripts/setup-environment.cjs", "health:check": "curl -f http://localhost:3002/api/health || echo 'API not responding'", "clean": "rm -rf dist node_modules/.vite", "clean:all": "rm -rf dist node_modules backend/node_modules"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.80.5", "axios": "^1.7.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.11.17", "plyr": "^3.7.8", "plyr-react": "^5.3.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.28.0", "sweetalert2": "^11.14.5"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.80.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "concurrently": "^9.1.0", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "postcss": "^8.5.1", "tailwindcss": "^3.4.14", "vite": "^6.0.1"}}