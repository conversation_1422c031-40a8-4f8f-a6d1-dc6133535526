{"version": 3, "sources": ["../../src/hooks/useDragDropManager.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { DragDropManager } from 'dnd-core'\nimport { useContext } from 'react'\n\nimport { DndContext } from '../core/index.js'\n\n/**\n * A hook to retrieve the DragDropManager from Context\n */\nexport function useDragDropManager(): DragDropManager {\n\tconst { dragDropManager } = useContext(DndContext)\n\tinvariant(dragDropManager != null, 'Expected drag drop context')\n\treturn dragDropManager as DragDropManager\n}\n"], "names": ["invariant", "useContext", "DndContext", "useDragDropManager", "dragDropManager"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAEhD,SAASC,UAAU,QAAQ,OAAO,CAAA;AAElC,SAASC,UAAU,QAAQ,kBAAkB,CAAA;AAE7C;;GAEG,CACH,OAAO,SAASC,kBAAkB,GAAoB;IACrD,MAAM,EAAEC,eAAe,CAAA,EAAE,GAAGH,UAAU,CAACC,UAAU,CAAC;IAClDF,SAAS,CAACI,eAAe,IAAI,IAAI,EAAE,4BAA4B,CAAC;IAChE,OAAOA,eAAe,CAAmB;CACzC"}