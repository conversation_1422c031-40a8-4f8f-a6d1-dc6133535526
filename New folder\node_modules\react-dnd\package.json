{"name": "react-dnd", "version": "16.0.1", "description": "Drag and Drop for React", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/react-dnd/react-dnd.git"}, "license": "MIT", "scripts": {"clean": "shx rm -rf dist/", "build_types": "tsc -b .", "build_esm": "swc -C module.type=es6 -d dist src/", "build": "run-s build_types build_esm"}, "dependencies": {"@react-dnd/invariant": "^4.0.1", "@react-dnd/shallowequal": "^4.0.1", "dnd-core": "^16.0.1", "fast-deep-equal": "^3.1.3", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.2.168", "@testing-library/react": "^13.1.1", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.25", "@types/react": "^18.0.5", "@types/react-dom": "^18.0.1", "jest": "^27.5.1", "jest-mock": "^27.5.1", "npm-run-all": "^4.1.5", "react": "^18.0.0", "react-dnd-test-backend": "portal:../backend-test", "react-dnd-test-utils": "portal:../test-utils", "react-dom": "^18.0.0", "shx": "^0.3.4", "typescript": "^4.6.3"}, "peerDependencies": {"@types/hoist-non-react-statics": ">= 3.3.1", "@types/node": ">= 12", "@types/react": ">= 16", "react": ">= 16.14"}, "peerDependenciesMeta": {"@types/hoist-non-react-statics": {"optional": true}, "@types/node": {"optional": true}, "@types/react": {"optional": true}}}