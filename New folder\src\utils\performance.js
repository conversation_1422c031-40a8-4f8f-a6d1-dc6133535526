// Performance monitoring and optimization utilities

/**
 * Measure and log performance metrics
 */
export const performanceMonitor = {
  // Track API call performance
  trackApiCall: (name, startTime) => {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    if (import.meta.env.DEV) {
      console.log(`🚀 API Call [${name}]: ${duration.toFixed(2)}ms`)
    }
    
    // In production, you might want to send this to an analytics service
    if (duration > 1000) {
      console.warn(`⚠️ Slow API call detected: ${name} took ${duration.toFixed(2)}ms`)
    }
    
    return duration
  },
  
  // Track component render performance
  trackRender: (componentName, startTime) => {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    if (import.meta.env.DEV && duration > 16) { // 16ms = 60fps threshold
      console.warn(`⚠️ Slow render: ${componentName} took ${duration.toFixed(2)}ms`)
    }
    
    return duration
  },
  
  // Track React Query cache hits/misses
  trackCachePerformance: (queryKey, isHit) => {
    if (import.meta.env.DEV) {
      const status = isHit ? '✅ Cache HIT' : '❌ Cache MISS'
      console.log(`${status}: ${JSON.stringify(queryKey)}`)
    }
  },
  
  // Monitor memory usage
  trackMemoryUsage: () => {
    if (performance.memory) {
      const memory = performance.memory
      const used = Math.round(memory.usedJSHeapSize / 1048576) // Convert to MB
      const total = Math.round(memory.totalJSHeapSize / 1048576)
      const limit = Math.round(memory.jsHeapSizeLimit / 1048576)
      
      if (import.meta.env.DEV) {
        console.log(`💾 Memory: ${used}MB / ${total}MB (Limit: ${limit}MB)`)
      }
      
      // Warn if memory usage is high
      if (used > limit * 0.8) {
        console.warn(`⚠️ High memory usage: ${used}MB (${((used/limit)*100).toFixed(1)}%)`)
      }
      
      return { used, total, limit }
    }
    return null
  }
}

/**
 * React Query performance optimizations
 */
export const queryOptimizations = {
  // Selective invalidation to avoid unnecessary refetches
  invalidateSelectively: (queryClient, pattern) => {
    return queryClient.invalidateQueries({
      predicate: (query) => {
        const queryKey = query.queryKey
        return queryKey.some(key => 
          typeof key === 'string' && key.includes(pattern)
        )
      }
    })
  },
  
  // Prefetch related data
  prefetchRelated: async (queryClient, courseId) => {
    const prefetchPromises = [
      queryClient.prefetchQuery({
        queryKey: ['courses', 'curriculum', courseId],
        staleTime: 30 * 1000
      }),
      queryClient.prefetchQuery({
        queryKey: ['courses', 'sections', courseId],
        staleTime: 30 * 1000
      })
    ]
    
    await Promise.allSettled(prefetchPromises)
  },
  
  // Optimize query keys for better caching
  createOptimizedKey: (entity, id, params = {}) => {
    // Sort params to ensure consistent keys
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key]
        return result
      }, {})
    
    return [entity, id, sortedParams].filter(Boolean)
  }
}

/**
 * Bundle size optimization helpers
 */
export const bundleOptimizations = {
  // Lazy load heavy components
  createLazyComponent: (importFn, fallback = null) => {
    const LazyComponent = React.lazy(importFn)
    
    return (props) => (
      <React.Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </React.Suspense>
    )
  },
  
  // Dynamic imports for heavy libraries
  loadLibrary: async (libraryName) => {
    const startTime = performance.now()
    
    try {
      let library
      
      switch (libraryName) {
        case 'chart.js':
          library = await import('chart.js')
          break
        case 'pdf-lib':
          library = await import('pdf-lib')
          break
        case 'xlsx':
          library = await import('xlsx')
          break
        default:
          throw new Error(`Unknown library: ${libraryName}`)
      }
      
      performanceMonitor.trackApiCall(`Load ${libraryName}`, startTime)
      return library
    } catch (error) {
      console.error(`Failed to load library ${libraryName}:`, error)
      throw error
    }
  }
}

/**
 * Network optimization
 */
export const networkOptimizations = {
  // Compress requests
  compressRequest: (data) => {
    // In a real implementation, you might use a compression library
    // For now, just remove empty fields
    if (typeof data === 'object' && data !== null) {
      const compressed = {}
      Object.keys(data).forEach(key => {
        const value = data[key]
        if (value !== null && value !== undefined && value !== '') {
          compressed[key] = value
        }
      })
      return compressed
    }
    return data
  },
  
  // Batch multiple requests
  createBatcher: (batchFn, delay = 50) => {
    let batch = []
    let timeoutId = null
    
    return (item) => {
      batch.push(item)
      
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = setTimeout(() => {
        const currentBatch = [...batch]
        batch = []
        batchFn(currentBatch)
      }, delay)
    }
  },
  
  // Retry with exponential backoff
  retryWithBackoff: async (fn, maxRetries = 3, baseDelay = 1000) => {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        if (i === maxRetries - 1) throw error
        
        const delay = baseDelay * Math.pow(2, i)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }
}

/**
 * Image optimization
 */
export const imageOptimizations = {
  // Create responsive image URLs
  createResponsiveUrl: (baseUrl, sizes = [300, 600, 1200]) => {
    return sizes.map(size => ({
      url: `${baseUrl}?w=${size}&q=80&f=auto`,
      width: size
    }))
  },
  
  // Lazy load images
  createLazyImage: (src, alt, className = '') => {
    const [loaded, setLoaded] = React.useState(false)
    const [inView, setInView] = React.useState(false)
    const imgRef = React.useRef()
    
    React.useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setInView(true)
            observer.disconnect()
          }
        },
        { threshold: 0.1 }
      )
      
      if (imgRef.current) {
        observer.observe(imgRef.current)
      }
      
      return () => observer.disconnect()
    }, [])
    
    return (
      <div ref={imgRef} className={className}>
        {inView && (
          <img
            src={src}
            alt={alt}
            onLoad={() => setLoaded(true)}
            style={{
              opacity: loaded ? 1 : 0,
              transition: 'opacity 0.3s ease'
            }}
          />
        )}
      </div>
    )
  }
}

/**
 * Performance monitoring hook
 */
export const usePerformanceMonitor = (componentName) => {
  React.useEffect(() => {
    const startTime = performance.now()
    
    return () => {
      performanceMonitor.trackRender(componentName, startTime)
    }
  }, [componentName])
  
  const trackAction = React.useCallback((actionName, fn) => {
    const startTime = performance.now()
    const result = fn()
    
    if (result && typeof result.then === 'function') {
      return result.finally(() => {
        performanceMonitor.trackApiCall(`${componentName}.${actionName}`, startTime)
      })
    } else {
      performanceMonitor.trackApiCall(`${componentName}.${actionName}`, startTime)
      return result
    }
  }, [componentName])
  
  return { trackAction }
}

export default {
  performanceMonitor,
  queryOptimizations,
  bundleOptimizations,
  networkOptimizations,
  imageOptimizations,
  usePerformanceMonitor
}
