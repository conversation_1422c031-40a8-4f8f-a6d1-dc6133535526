{"version": 3, "sources": ["../../src/internals/SourceConnector.ts"], "sourcesContent": ["import { shallowEqual } from '@react-dnd/shallowequal'\nimport type { Backend, Identifier, Unsubscribe } from 'dnd-core'\nimport type { ReactElement, Ref, RefObject } from 'react'\n\nimport type { DragPreviewOptions, DragSourceOptions } from '../types/index.js'\nimport { isRef } from './isRef.js'\nimport { wrapConnectorHooks } from './wrapConnectorHooks.js'\n\nexport interface Connector {\n\thooks: any\n\tconnectTarget: any\n\treceiveHandlerId(handlerId: Identifier | null): void\n\treconnect(): void\n}\n\nexport class SourceConnector implements Connector {\n\tpublic hooks = wrapConnectorHooks({\n\t\tdragSource: (\n\t\t\tnode: Element | ReactElement | Ref<any>,\n\t\t\toptions?: DragSourceOptions,\n\t\t) => {\n\t\t\tthis.clearDragSource()\n\t\t\tthis.dragSourceOptions = options || null\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dragSourceRef = node as RefObject<any>\n\t\t\t} else {\n\t\t\t\tthis.dragSourceNode = node\n\t\t\t}\n\t\t\tthis.reconnectDragSource()\n\t\t},\n\t\tdragPreview: (node: any, options?: DragPreviewOptions) => {\n\t\t\tthis.clearDragPreview()\n\t\t\tthis.dragPreviewOptions = options || null\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dragPreviewRef = node\n\t\t\t} else {\n\t\t\t\tthis.dragPreviewNode = node\n\t\t\t}\n\t\t\tthis.reconnectDragPreview()\n\t\t},\n\t})\n\tprivate handlerId: Identifier | null = null\n\n\t// The drop target may either be attached via ref or connect function\n\tprivate dragSourceRef: RefObject<any> | null = null\n\tprivate dragSourceNode: any\n\tprivate dragSourceOptionsInternal: DragSourceOptions | null = null\n\tprivate dragSourceUnsubscribe: Unsubscribe | undefined\n\n\t// The drag preview may either be attached via ref or connect function\n\tprivate dragPreviewRef: RefObject<any> | null = null\n\tprivate dragPreviewNode: any\n\tprivate dragPreviewOptionsInternal: DragPreviewOptions | null = null\n\tprivate dragPreviewUnsubscribe: Unsubscribe | undefined\n\n\tprivate lastConnectedHandlerId: Identifier | null = null\n\tprivate lastConnectedDragSource: any = null\n\tprivate lastConnectedDragSourceOptions: any = null\n\tprivate lastConnectedDragPreview: any = null\n\tprivate lastConnectedDragPreviewOptions: any = null\n\n\tprivate readonly backend: Backend\n\n\tpublic constructor(backend: Backend) {\n\t\tthis.backend = backend\n\t}\n\n\tpublic receiveHandlerId(newHandlerId: Identifier | null): void {\n\t\tif (this.handlerId === newHandlerId) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.handlerId = newHandlerId\n\t\tthis.reconnect()\n\t}\n\n\tpublic get connectTarget(): any {\n\t\treturn this.dragSource\n\t}\n\n\tpublic get dragSourceOptions(): DragSourceOptions | null {\n\t\treturn this.dragSourceOptionsInternal\n\t}\n\tpublic set dragSourceOptions(options: DragSourceOptions | null) {\n\t\tthis.dragSourceOptionsInternal = options\n\t}\n\n\tpublic get dragPreviewOptions(): DragPreviewOptions | null {\n\t\treturn this.dragPreviewOptionsInternal\n\t}\n\n\tpublic set dragPreviewOptions(options: DragPreviewOptions | null) {\n\t\tthis.dragPreviewOptionsInternal = options\n\t}\n\n\tpublic reconnect(): void {\n\t\tconst didChange = this.reconnectDragSource()\n\t\tthis.reconnectDragPreview(didChange)\n\t}\n\n\tprivate reconnectDragSource(): boolean {\n\t\tconst dragSource = this.dragSource\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didConnectedDragSourceChange() ||\n\t\t\tthis.didDragSourceOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDragSource()\n\t\t}\n\n\t\tif (!this.handlerId) {\n\t\t\treturn didChange\n\t\t}\n\t\tif (!dragSource) {\n\t\t\tthis.lastConnectedDragSource = dragSource\n\t\t\treturn didChange\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDragSource = dragSource\n\t\t\tthis.lastConnectedDragSourceOptions = this.dragSourceOptions\n\t\t\tthis.dragSourceUnsubscribe = this.backend.connectDragSource(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdragSource,\n\t\t\t\tthis.dragSourceOptions,\n\t\t\t)\n\t\t}\n\t\treturn didChange\n\t}\n\n\tprivate reconnectDragPreview(forceDidChange = false): void {\n\t\tconst dragPreview = this.dragPreview\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tforceDidChange ||\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didConnectedDragPreviewChange() ||\n\t\t\tthis.didDragPreviewOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDragPreview()\n\t\t}\n\n\t\tif (!this.handlerId) {\n\t\t\treturn\n\t\t}\n\t\tif (!dragPreview) {\n\t\t\tthis.lastConnectedDragPreview = dragPreview\n\t\t\treturn\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDragPreview = dragPreview\n\t\t\tthis.lastConnectedDragPreviewOptions = this.dragPreviewOptions\n\t\t\tthis.dragPreviewUnsubscribe = this.backend.connectDragPreview(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdragPreview,\n\t\t\t\tthis.dragPreviewOptions,\n\t\t\t)\n\t\t}\n\t}\n\n\tprivate didHandlerIdChange(): boolean {\n\t\treturn this.lastConnectedHandlerId !== this.handlerId\n\t}\n\n\tprivate didConnectedDragSourceChange(): boolean {\n\t\treturn this.lastConnectedDragSource !== this.dragSource\n\t}\n\n\tprivate didConnectedDragPreviewChange(): boolean {\n\t\treturn this.lastConnectedDragPreview !== this.dragPreview\n\t}\n\n\tprivate didDragSourceOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDragSourceOptions,\n\t\t\tthis.dragSourceOptions,\n\t\t)\n\t}\n\n\tprivate didDragPreviewOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDragPreviewOptions,\n\t\t\tthis.dragPreviewOptions,\n\t\t)\n\t}\n\n\tpublic disconnectDragSource() {\n\t\tif (this.dragSourceUnsubscribe) {\n\t\t\tthis.dragSourceUnsubscribe()\n\t\t\tthis.dragSourceUnsubscribe = undefined\n\t\t}\n\t}\n\n\tpublic disconnectDragPreview() {\n\t\tif (this.dragPreviewUnsubscribe) {\n\t\t\tthis.dragPreviewUnsubscribe()\n\t\t\tthis.dragPreviewUnsubscribe = undefined\n\t\t\tthis.dragPreviewNode = null\n\t\t\tthis.dragPreviewRef = null\n\t\t}\n\t}\n\n\tprivate get dragSource() {\n\t\treturn (\n\t\t\tthis.dragSourceNode || (this.dragSourceRef && this.dragSourceRef.current)\n\t\t)\n\t}\n\n\tprivate get dragPreview() {\n\t\treturn (\n\t\t\tthis.dragPreviewNode ||\n\t\t\t(this.dragPreviewRef && this.dragPreviewRef.current)\n\t\t)\n\t}\n\n\tprivate clearDragSource() {\n\t\tthis.dragSourceNode = null\n\t\tthis.dragSourceRef = null\n\t}\n\n\tprivate clearDragPreview() {\n\t\tthis.dragPreviewNode = null\n\t\tthis.dragPreviewRef = null\n\t}\n}\n"], "names": ["shallowEqual", "isRef", "wrapConnectorHooks", "SourceConnector", "receiveHandlerId", "newHandlerId", "handlerId", "reconnect", "connectTarget", "dragSource", "dragSourceOptions", "dragSourceOptionsInternal", "options", "dragPreviewOptions", "dragPreviewOptionsInternal", "<PERSON><PERSON><PERSON><PERSON>", "reconnectDragSource", "reconnectDragPreview", "didHandlerIdChange", "didConnectedDragSourceChange", "didDragSourceOptionsChange", "disconnectDragSource", "lastConnectedDragSource", "lastConnectedHandlerId", "lastConnectedDragSourceOptions", "dragSourceUnsubscribe", "backend", "connectDragSource", "forceDidChange", "dragPreview", "didConnectedDragPreviewChange", "didDragPreviewOptionsChange", "disconnectDragPreview", "lastConnectedDragPreview", "lastConnectedDragPreviewOptions", "dragPreviewUnsubscribe", "connectDragPreview", "undefined", "dragPreviewNode", "dragPreviewRef", "dragSourceNode", "dragSourceRef", "current", "clearDragSource", "clearDragPreview", "hooks", "node"], "mappings": "AAAA,SAASA,YAAY,QAAQ,yBAAyB,CAAA;AAKtD,SAASC,KAAK,QAAQ,YAAY,CAAA;AAClC,SAASC,kBAAkB,QAAQ,yBAAyB,CAAA;AAS5D,OAAO,MAAMC,eAAe;IAoD3B,AAAOC,gBAAgB,CAACC,YAA+B,EAAQ;QAC9D,IAAI,IAAI,CAACC,SAAS,KAAKD,YAAY,EAAE;YACpC,OAAM;SACN;QAED,IAAI,CAACC,SAAS,GAAGD,YAAY;QAC7B,IAAI,CAACE,SAAS,EAAE;KAChB;IAED,IAAWC,aAAa,GAAQ;QAC/B,OAAO,IAAI,CAACC,UAAU,CAAA;KACtB;IAED,IAAWC,iBAAiB,GAA6B;QACxD,OAAO,IAAI,CAACC,yBAAyB,CAAA;KACrC;IACD,IAAWD,iBAAiB,CAACE,OAAiC,EAAE;QAC/D,IAAI,CAACD,yBAAyB,GAAGC,OAAO;KACxC;IAED,IAAWC,kBAAkB,GAA8B;QAC1D,OAAO,IAAI,CAACC,0BAA0B,CAAA;KACtC;IAED,IAAWD,kBAAkB,CAACD,OAAkC,EAAE;QACjE,IAAI,CAACE,0BAA0B,GAAGF,OAAO;KACzC;IAED,AAAOL,SAAS,GAAS;QACxB,MAAMQ,SAAS,GAAG,IAAI,CAACC,mBAAmB,EAAE;QAC5C,IAAI,CAACC,oBAAoB,CAACF,SAAS,CAAC;KACpC;IAED,AAAQC,mBAAmB,GAAY;QACtC,MAAMP,UAAU,GAAG,IAAI,CAACA,UAAU;QAClC,gDAAgD;QAChD,MAAMM,SAAS,GACd,IAAI,CAACG,kBAAkB,EAAE,IACzB,IAAI,CAACC,4BAA4B,EAAE,IACnC,IAAI,CAACC,0BAA0B,EAAE;QAElC,IAAIL,SAAS,EAAE;YACd,IAAI,CAACM,oBAAoB,EAAE;SAC3B;QAED,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;YACpB,OAAOS,SAAS,CAAA;SAChB;QACD,IAAI,CAACN,UAAU,EAAE;YAChB,IAAI,CAACa,uBAAuB,GAAGb,UAAU;YACzC,OAAOM,SAAS,CAAA;SAChB;QAED,IAAIA,SAAS,EAAE;YACd,IAAI,CAACQ,sBAAsB,GAAG,IAAI,CAACjB,SAAS;YAC5C,IAAI,CAACgB,uBAAuB,GAAGb,UAAU;YACzC,IAAI,CAACe,8BAA8B,GAAG,IAAI,CAACd,iBAAiB;YAC5D,IAAI,CAACe,qBAAqB,GAAG,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAC1D,IAAI,CAACrB,SAAS,EACdG,UAAU,EACV,IAAI,CAACC,iBAAiB,CACtB;SACD;QACD,OAAOK,SAAS,CAAA;KAChB;IAED,AAAQE,oBAAoB,CAACW,cAAc,GAAG,KAAK,EAAQ;QAC1D,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;QACpC,gDAAgD;QAChD,MAAMd,SAAS,GACda,cAAc,IACd,IAAI,CAACV,kBAAkB,EAAE,IACzB,IAAI,CAACY,6BAA6B,EAAE,IACpC,IAAI,CAACC,2BAA2B,EAAE;QAEnC,IAAIhB,SAAS,EAAE;YACd,IAAI,CAACiB,qBAAqB,EAAE;SAC5B;QAED,IAAI,CAAC,IAAI,CAAC1B,SAAS,EAAE;YACpB,OAAM;SACN;QACD,IAAI,CAACuB,WAAW,EAAE;YACjB,IAAI,CAACI,wBAAwB,GAAGJ,WAAW;YAC3C,OAAM;SACN;QAED,IAAId,SAAS,EAAE;YACd,IAAI,CAACQ,sBAAsB,GAAG,IAAI,CAACjB,SAAS;YAC5C,IAAI,CAAC2B,wBAAwB,GAAGJ,WAAW;YAC3C,IAAI,CAACK,+BAA+B,GAAG,IAAI,CAACrB,kBAAkB;YAC9D,IAAI,CAACsB,sBAAsB,GAAG,IAAI,CAACT,OAAO,CAACU,kBAAkB,CAC5D,IAAI,CAAC9B,SAAS,EACduB,WAAW,EACX,IAAI,CAAChB,kBAAkB,CACvB;SACD;KACD;IAED,AAAQK,kBAAkB,GAAY;QACrC,OAAO,IAAI,CAACK,sBAAsB,KAAK,IAAI,CAACjB,SAAS,CAAA;KACrD;IAED,AAAQa,4BAA4B,GAAY;QAC/C,OAAO,IAAI,CAACG,uBAAuB,KAAK,IAAI,CAACb,UAAU,CAAA;KACvD;IAED,AAAQqB,6BAA6B,GAAY;QAChD,OAAO,IAAI,CAACG,wBAAwB,KAAK,IAAI,CAACJ,WAAW,CAAA;KACzD;IAED,AAAQT,0BAA0B,GAAY;QAC7C,OAAO,CAACpB,YAAY,CACnB,IAAI,CAACwB,8BAA8B,EACnC,IAAI,CAACd,iBAAiB,CACtB,CAAA;KACD;IAED,AAAQqB,2BAA2B,GAAY;QAC9C,OAAO,CAAC/B,YAAY,CACnB,IAAI,CAACkC,+BAA+B,EACpC,IAAI,CAACrB,kBAAkB,CACvB,CAAA;KACD;IAED,AAAOQ,oBAAoB,GAAG;QAC7B,IAAI,IAAI,CAACI,qBAAqB,EAAE;YAC/B,IAAI,CAACA,qBAAqB,EAAE;YAC5B,IAAI,CAACA,qBAAqB,GAAGY,SAAS;SACtC;KACD;IAED,AAAOL,qBAAqB,GAAG;QAC9B,IAAI,IAAI,CAACG,sBAAsB,EAAE;YAChC,IAAI,CAACA,sBAAsB,EAAE;YAC7B,IAAI,CAACA,sBAAsB,GAAGE,SAAS;YACvC,IAAI,CAACC,eAAe,GAAG,IAAI;YAC3B,IAAI,CAACC,cAAc,GAAG,IAAI;SAC1B;KACD;IAED,IAAY9B,UAAU,GAAG;QACxB,OACC,IAAI,CAAC+B,cAAc,IAAK,IAAI,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,OAAO,AAAC,CACzE;KACD;IAED,IAAYb,WAAW,GAAG;QACzB,OACC,IAAI,CAACS,eAAe,IACnB,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACG,OAAO,AAAC,CACpD;KACD;IAED,AAAQC,eAAe,GAAG;QACzB,IAAI,CAACH,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;KACzB;IAED,AAAQG,gBAAgB,GAAG;QAC1B,IAAI,CAACN,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACC,cAAc,GAAG,IAAI;KAC1B;IAtKD,YAAmBb,OAAgB,CAAE;QA/CrC,KAAOmB,KAAK,GAAG3C,kBAAkB,CAAC;YACjCO,UAAU,EAAE,CACXqC,IAAuC,EACvClC,OAA2B,GACvB;gBACJ,IAAI,CAAC+B,eAAe,EAAE;gBACtB,IAAI,CAACjC,iBAAiB,GAAGE,OAAO,IAAI,IAAI;gBACxC,IAAIX,KAAK,CAAC6C,IAAI,CAAC,EAAE;oBAChB,IAAI,CAACL,aAAa,GAAGK,IAAI,AAAkB;iBAC3C,MAAM;oBACN,IAAI,CAACN,cAAc,GAAGM,IAAI;iBAC1B;gBACD,IAAI,CAAC9B,mBAAmB,EAAE;aAC1B;YACDa,WAAW,EAAE,CAACiB,IAAS,EAAElC,OAA4B,GAAK;gBACzD,IAAI,CAACgC,gBAAgB,EAAE;gBACvB,IAAI,CAAC/B,kBAAkB,GAAGD,OAAO,IAAI,IAAI;gBACzC,IAAIX,KAAK,CAAC6C,IAAI,CAAC,EAAE;oBAChB,IAAI,CAACP,cAAc,GAAGO,IAAI;iBAC1B,MAAM;oBACN,IAAI,CAACR,eAAe,GAAGQ,IAAI;iBAC3B;gBACD,IAAI,CAAC7B,oBAAoB,EAAE;aAC3B;SACD,CAAC,AAxCH,CAwCG;QACF,KAAQX,SAAS,GAAsB,IAAI,AAzC5C,CAyC4C;QAE3C,qEAAqE;QACrE,KAAQmC,aAAa,GAA0B,IAAI,AA5CpD,CA4CoD;QAEnD,KAAQ9B,yBAAyB,GAA6B,IAAI,AA9CnE,CA8CmE;QAGlE,sEAAsE;QACtE,KAAQ4B,cAAc,GAA0B,IAAI,AAlDrD,CAkDqD;QAEpD,KAAQzB,0BAA0B,GAA8B,IAAI,AApDrE,CAoDqE;QAGpE,KAAQS,sBAAsB,GAAsB,IAAI,AAvDzD,CAuDyD;QACxD,KAAQD,uBAAuB,GAAQ,IAAI,AAxD5C,CAwD4C;QAC3C,KAAQE,8BAA8B,GAAQ,IAAI,AAzDnD,CAyDmD;QAClD,KAAQS,wBAAwB,GAAQ,IAAI,AA1D7C,CA0D6C;QAC5C,KAAQC,+BAA+B,GAAQ,IAAI,AA3DpD,CA2DoD;QAKlD,IAAI,CAACR,OAAO,GAAGA,OAAO;KACtB;CAqKD"}