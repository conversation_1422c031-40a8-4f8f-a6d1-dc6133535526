{"version": 3, "sources": ["../../src/classes/DragDropMonitorImpl.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { Store } from 'redux'\n\nimport type {\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from '../interfaces.js'\nimport type { State } from '../reducers/index.js'\nimport {\n\tgetDifferenceFromInitialOffset,\n\tgetSourceClientOffset,\n} from '../utils/coords.js'\nimport { areDirty } from '../utils/dirtiness.js'\nimport { matchesType } from '../utils/matchesType.js'\n\nexport class DragDropMonitorImpl implements DragDropMonitor {\n\tprivate store: Store<State>\n\tpublic readonly registry: HandlerRegistry\n\n\tpublic constructor(store: Store<State>, registry: HandlerRegistry) {\n\t\tthis.store = store\n\t\tthis.registry = registry\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions: { handlerIds?: string[] } = {},\n\t): Unsubscribe {\n\t\tconst { handlerIds } = options\n\t\tinvariant(typeof listener === 'function', 'listener must be a function.')\n\t\tinvariant(\n\t\t\ttypeof handlerIds === 'undefined' || Array.isArray(handlerIds),\n\t\t\t'handlerIds, when specified, must be an array of strings.',\n\t\t)\n\n\t\tlet prevStateId = this.store.getState().stateId\n\t\tconst handleChange = () => {\n\t\t\tconst state = this.store.getState()\n\t\t\tconst currentStateId = state.stateId\n\t\t\ttry {\n\t\t\t\tconst canSkipListener =\n\t\t\t\t\tcurrentStateId === prevStateId ||\n\t\t\t\t\t(currentStateId === prevStateId + 1 &&\n\t\t\t\t\t\t!areDirty(state.dirtyHandlerIds, handlerIds))\n\n\t\t\t\tif (!canSkipListener) {\n\t\t\t\t\tlistener()\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tprevStateId = currentStateId\n\t\t\t}\n\t\t}\n\n\t\treturn this.store.subscribe(handleChange)\n\t}\n\n\tpublic subscribeToOffsetChange(listener: Listener): Unsubscribe {\n\t\tinvariant(typeof listener === 'function', 'listener must be a function.')\n\n\t\tlet previousState = this.store.getState().dragOffset\n\t\tconst handleChange = () => {\n\t\t\tconst nextState = this.store.getState().dragOffset\n\t\t\tif (nextState === previousState) {\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tpreviousState = nextState\n\t\t\tlistener()\n\t\t}\n\n\t\treturn this.store.subscribe(handleChange)\n\t}\n\n\tpublic canDragSource(sourceId: string | undefined): boolean {\n\t\tif (!sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tconst source = this.registry.getSource(sourceId)\n\t\tinvariant(source, `Expected to find a valid source. sourceId=${sourceId}`)\n\n\t\tif (this.isDragging()) {\n\t\t\treturn false\n\t\t}\n\n\t\treturn source.canDrag(this, sourceId)\n\t}\n\n\tpublic canDropOnTarget(targetId: string | undefined): boolean {\n\t\t// undefined on initial render\n\t\tif (!targetId) {\n\t\t\treturn false\n\t\t}\n\t\tconst target = this.registry.getTarget(targetId)\n\t\tinvariant(target, `Expected to find a valid target. targetId=${targetId}`)\n\n\t\tif (!this.isDragging() || this.didDrop()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetType = this.registry.getTargetType(targetId)\n\t\tconst draggedItemType = this.getItemType()\n\t\treturn (\n\t\t\tmatchesType(targetType, draggedItemType) && target.canDrop(this, targetId)\n\t\t)\n\t}\n\n\tpublic isDragging(): boolean {\n\t\treturn Boolean(this.getItemType())\n\t}\n\n\tpublic isDraggingSource(sourceId: string | undefined): boolean {\n\t\t// undefined on initial render\n\t\tif (!sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tconst source = this.registry.getSource(sourceId, true)\n\t\tinvariant(source, `Expected to find a valid source. sourceId=${sourceId}`)\n\n\t\tif (!this.isDragging() || !this.isSourcePublic()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst sourceType = this.registry.getSourceType(sourceId)\n\t\tconst draggedItemType = this.getItemType()\n\t\tif (sourceType !== draggedItemType) {\n\t\t\treturn false\n\t\t}\n\n\t\treturn source.isDragging(this, sourceId)\n\t}\n\n\tpublic isOverTarget(\n\t\ttargetId: string | undefined,\n\t\toptions = { shallow: false },\n\t): boolean {\n\t\t// undefined on initial render\n\t\tif (!targetId) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst { shallow } = options\n\t\tif (!this.isDragging()) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetType = this.registry.getTargetType(targetId)\n\t\tconst draggedItemType = this.getItemType()\n\t\tif (draggedItemType && !matchesType(targetType, draggedItemType)) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst targetIds = this.getTargetIds()\n\t\tif (!targetIds.length) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst index = targetIds.indexOf(targetId)\n\t\tif (shallow) {\n\t\t\treturn index === targetIds.length - 1\n\t\t} else {\n\t\t\treturn index > -1\n\t\t}\n\t}\n\n\tpublic getItemType(): Identifier {\n\t\treturn this.store.getState().dragOperation.itemType as Identifier\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.store.getState().dragOperation.item\n\t}\n\n\tpublic getSourceId(): string | null {\n\t\treturn this.store.getState().dragOperation.sourceId\n\t}\n\n\tpublic getTargetIds(): string[] {\n\t\treturn this.store.getState().dragOperation.targetIds\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.store.getState().dragOperation.dropResult\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.store.getState().dragOperation.didDrop\n\t}\n\n\tpublic isSourcePublic(): boolean {\n\t\treturn Boolean(this.store.getState().dragOperation.isSourcePublic)\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.initialClientOffset\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.initialSourceClientOffset\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.store.getState().dragOffset.clientOffset\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn getSourceClientOffset(this.store.getState().dragOffset)\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn getDifferenceFromInitialOffset(this.store.getState().dragOffset)\n\t}\n}\n"], "names": ["invariant", "getDifferenceFromInitialOffset", "getSourceClientOffset", "areDirty", "matchesType", "DragDropMonitorImpl", "subscribeToStateChange", "listener", "options", "handlerIds", "Array", "isArray", "prevStateId", "store", "getState", "stateId", "handleChange", "state", "currentStateId", "canSkipListener", "dirtyHandlerIds", "subscribe", "subscribeToOffsetChange", "previousState", "dragOffset", "nextState", "canDragSource", "sourceId", "source", "registry", "getSource", "isDragging", "canDrag", "canDropOnTarget", "targetId", "target", "get<PERSON><PERSON><PERSON>", "didDrop", "targetType", "getTargetType", "draggedItemType", "getItemType", "canDrop", "Boolean", "isDraggingSource", "isSourcePublic", "sourceType", "getSourceType", "isOverTarget", "shallow", "targetIds", "getTargetIds", "length", "index", "indexOf", "dragOperation", "itemType", "getItem", "item", "getSourceId", "getDropResult", "dropResult", "getInitialClientOffset", "initialClientOffset", "getInitialSourceClientOffset", "initialSourceClientOffset", "getClientOffset", "clientOffset"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAYhD,SACCC,8BAA8B,EAC9BC,qBAAqB,QACf,oBAAoB,CAAA;AAC3B,SAASC,QAAQ,QAAQ,uBAAuB,CAAA;AAChD,SAASC,WAAW,QAAQ,yBAAyB,CAAA;AAErD,OAAO,MAAMC,mBAAmB;IAS/B,AAAOC,sBAAsB,CAC5BC,QAAkB,EAClBC,OAAkC,GAAG,EAAE,EACzB;QACd,MAAM,EAAEC,UAAU,CAAA,EAAE,GAAGD,OAAO;QAC9BR,SAAS,CAAC,OAAOO,QAAQ,KAAK,UAAU,EAAE,8BAA8B,CAAC;QACzEP,SAAS,CACR,OAAOS,UAAU,KAAK,WAAW,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAC9D,0DAA0D,CAC1D;QAED,IAAIG,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,EAAE,CAACC,OAAO;QAC/C,MAAMC,YAAY,GAAG,IAAM;YAC1B,MAAMC,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACC,QAAQ,EAAE;YACnC,MAAMI,cAAc,GAAGD,KAAK,CAACF,OAAO;YACpC,IAAI;gBACH,MAAMI,eAAe,GACpBD,cAAc,KAAKN,WAAW,IAC7BM,cAAc,KAAKN,WAAW,GAAG,CAAC,IAClC,CAACT,QAAQ,CAACc,KAAK,CAACG,eAAe,EAAEX,UAAU,CAAC,AAAC;gBAE/C,IAAI,CAACU,eAAe,EAAE;oBACrBZ,QAAQ,EAAE;iBACV;aACD,QAAS;gBACTK,WAAW,GAAGM,cAAc;aAC5B;SACD;QAED,OAAO,IAAI,CAACL,KAAK,CAACQ,SAAS,CAACL,YAAY,CAAC,CAAA;KACzC;IAED,AAAOM,uBAAuB,CAACf,QAAkB,EAAe;QAC/DP,SAAS,CAAC,OAAOO,QAAQ,KAAK,UAAU,EAAE,8BAA8B,CAAC;QAEzE,IAAIgB,aAAa,GAAG,IAAI,CAACV,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU;QACpD,MAAMR,YAAY,GAAG,IAAM;YAC1B,MAAMS,SAAS,GAAG,IAAI,CAACZ,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU;YAClD,IAAIC,SAAS,KAAKF,aAAa,EAAE;gBAChC,OAAM;aACN;YAEDA,aAAa,GAAGE,SAAS;YACzBlB,QAAQ,EAAE;SACV;QAED,OAAO,IAAI,CAACM,KAAK,CAACQ,SAAS,CAACL,YAAY,CAAC,CAAA;KACzC;IAED,AAAOU,aAAa,CAACC,QAA4B,EAAW;QAC3D,IAAI,CAACA,QAAQ,EAAE;YACd,OAAO,KAAK,CAAA;SACZ;QACD,MAAMC,MAAM,GAAG,IAAI,CAACC,QAAQ,CAACC,SAAS,CAACH,QAAQ,CAAC;QAChD3B,SAAS,CAAC4B,MAAM,EAAE,CAAC,0CAA0C,EAAED,QAAQ,CAAC,CAAC,CAAC;QAE1E,IAAI,IAAI,CAACI,UAAU,EAAE,EAAE;YACtB,OAAO,KAAK,CAAA;SACZ;QAED,OAAOH,MAAM,CAACI,OAAO,CAAC,IAAI,EAAEL,QAAQ,CAAC,CAAA;KACrC;IAED,AAAOM,eAAe,CAACC,QAA4B,EAAW;QAC7D,8BAA8B;QAC9B,IAAI,CAACA,QAAQ,EAAE;YACd,OAAO,KAAK,CAAA;SACZ;QACD,MAAMC,MAAM,GAAG,IAAI,CAACN,QAAQ,CAACO,SAAS,CAACF,QAAQ,CAAC;QAChDlC,SAAS,CAACmC,MAAM,EAAE,CAAC,0CAA0C,EAAED,QAAQ,CAAC,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAACH,UAAU,EAAE,IAAI,IAAI,CAACM,OAAO,EAAE,EAAE;YACzC,OAAO,KAAK,CAAA;SACZ;QAED,MAAMC,UAAU,GAAG,IAAI,CAACT,QAAQ,CAACU,aAAa,CAACL,QAAQ,CAAC;QACxD,MAAMM,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;QAC1C,OACCrC,WAAW,CAACkC,UAAU,EAAEE,eAAe,CAAC,IAAIL,MAAM,CAACO,OAAO,CAAC,IAAI,EAAER,QAAQ,CAAC,CAC1E;KACD;IAED,AAAOH,UAAU,GAAY;QAC5B,OAAOY,OAAO,CAAC,IAAI,CAACF,WAAW,EAAE,CAAC,CAAA;KAClC;IAED,AAAOG,gBAAgB,CAACjB,QAA4B,EAAW;QAC9D,8BAA8B;QAC9B,IAAI,CAACA,QAAQ,EAAE;YACd,OAAO,KAAK,CAAA;SACZ;QACD,MAAMC,MAAM,GAAG,IAAI,CAACC,QAAQ,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,CAAC;QACtD3B,SAAS,CAAC4B,MAAM,EAAE,CAAC,0CAA0C,EAAED,QAAQ,CAAC,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAACI,UAAU,EAAE,IAAI,CAAC,IAAI,CAACc,cAAc,EAAE,EAAE;YACjD,OAAO,KAAK,CAAA;SACZ;QAED,MAAMC,UAAU,GAAG,IAAI,CAACjB,QAAQ,CAACkB,aAAa,CAACpB,QAAQ,CAAC;QACxD,MAAMa,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;QAC1C,IAAIK,UAAU,KAAKN,eAAe,EAAE;YACnC,OAAO,KAAK,CAAA;SACZ;QAED,OAAOZ,MAAM,CAACG,UAAU,CAAC,IAAI,EAAEJ,QAAQ,CAAC,CAAA;KACxC;IAED,AAAOqB,YAAY,CAClBd,QAA4B,EAC5B1B,OAAO,GAAG;QAAEyC,OAAO,EAAE,KAAK;KAAE,EAClB;QACV,8BAA8B;QAC9B,IAAI,CAACf,QAAQ,EAAE;YACd,OAAO,KAAK,CAAA;SACZ;QAED,MAAM,EAAEe,OAAO,CAAA,EAAE,GAAGzC,OAAO;QAC3B,IAAI,CAAC,IAAI,CAACuB,UAAU,EAAE,EAAE;YACvB,OAAO,KAAK,CAAA;SACZ;QAED,MAAMO,UAAU,GAAG,IAAI,CAACT,QAAQ,CAACU,aAAa,CAACL,QAAQ,CAAC;QACxD,MAAMM,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;QAC1C,IAAID,eAAe,IAAI,CAACpC,WAAW,CAACkC,UAAU,EAAEE,eAAe,CAAC,EAAE;YACjE,OAAO,KAAK,CAAA;SACZ;QAED,MAAMU,SAAS,GAAG,IAAI,CAACC,YAAY,EAAE;QACrC,IAAI,CAACD,SAAS,CAACE,MAAM,EAAE;YACtB,OAAO,KAAK,CAAA;SACZ;QAED,MAAMC,KAAK,GAAGH,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAAC;QACzC,IAAIe,OAAO,EAAE;YACZ,OAAOI,KAAK,KAAKH,SAAS,CAACE,MAAM,GAAG,CAAC,CAAA;SACrC,MAAM;YACN,OAAOC,KAAK,GAAG,CAAC,CAAC,CAAA;SACjB;KACD;IAED,AAAOZ,WAAW,GAAe;QAChC,OAAO,IAAI,CAAC5B,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACC,QAAQ,CAAc;KACjE;IAED,AAAOC,OAAO,GAAQ;QACrB,OAAO,IAAI,CAAC5C,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACG,IAAI,CAAA;KAC/C;IAED,AAAOC,WAAW,GAAkB;QACnC,OAAO,IAAI,CAAC9C,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAAC5B,QAAQ,CAAA;KACnD;IAED,AAAOwB,YAAY,GAAa;QAC/B,OAAO,IAAI,CAACtC,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACL,SAAS,CAAA;KACpD;IAED,AAAOU,aAAa,GAAQ;QAC3B,OAAO,IAAI,CAAC/C,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACM,UAAU,CAAA;KACrD;IAED,AAAOxB,OAAO,GAAY;QACzB,OAAO,IAAI,CAACxB,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAAClB,OAAO,CAAA;KAClD;IAED,AAAOQ,cAAc,GAAY;QAChC,OAAOF,OAAO,CAAC,IAAI,CAAC9B,KAAK,CAACC,QAAQ,EAAE,CAACyC,aAAa,CAACV,cAAc,CAAC,CAAA;KAClE;IAED,AAAOiB,sBAAsB,GAAmB;QAC/C,OAAO,IAAI,CAACjD,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAACuC,mBAAmB,CAAA;KAC3D;IAED,AAAOC,4BAA4B,GAAmB;QACrD,OAAO,IAAI,CAACnD,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAACyC,yBAAyB,CAAA;KACjE;IAED,AAAOC,eAAe,GAAmB;QACxC,OAAO,IAAI,CAACrD,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAAC2C,YAAY,CAAA;KACpD;IAED,AAAOjE,qBAAqB,GAAmB;QAC9C,OAAOA,qBAAqB,CAAC,IAAI,CAACW,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAAC,CAAA;KAC9D;IAED,AAAOvB,8BAA8B,GAAmB;QACvD,OAAOA,8BAA8B,CAAC,IAAI,CAACY,KAAK,CAACC,QAAQ,EAAE,CAACU,UAAU,CAAC,CAAA;KACvE;IA/LD,YAAmBX,KAAmB,EAAEgB,QAAyB,CAAE;QAClE,IAAI,CAAChB,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACgB,QAAQ,GAAGA,QAAQ;KACxB;CA6LD"}