{"version": 3, "sources": ["../../src/NativeDragSources/nativeTypesConfig.ts"], "sourcesContent": ["import * as NativeTypes from '../NativeTypes.js'\nimport { getDataFromDataTransfer } from './getDataFromDataTransfer.js'\n\nexport interface NativeItemConfigExposePropreties {\n\t[property: string]: (\n\t\tdataTransfer: DataTransfer,\n\t\tmatchesTypes: string[],\n\t) => any\n}\n\nexport interface NativeItemConfig {\n\texposeProperties: NativeItemConfigExposePropreties\n\tmatchesTypes: string[]\n}\n\nexport const nativeTypesConfig: {\n\t[key: string]: NativeItemConfig\n} = {\n\t[NativeTypes.FILE]: {\n\t\texposeProperties: {\n\t\t\tfiles: (dataTransfer: DataTransfer): File[] =>\n\t\t\t\tArray.prototype.slice.call(dataTransfer.files),\n\t\t\titems: (dataTransfer: DataTransfer): DataTransferItemList =>\n\t\t\t\tdataTransfer.items,\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Files'],\n\t},\n\t[NativeTypes.HTML]: {\n\t\texposeProperties: {\n\t\t\thtml: (dataTransfer: DataTransfer, matchesTypes: string[]): string =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, ''),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Html', 'text/html'],\n\t},\n\t[NativeTypes.URL]: {\n\t\texposeProperties: {\n\t\t\turls: (dataTransfer: DataTransfer, matchesTypes: string[]): string[] =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, '').split('\\n'),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Url', 'text/uri-list'],\n\t},\n\t[NativeTypes.TEXT]: {\n\t\texposeProperties: {\n\t\t\ttext: (dataTransfer: DataTransfer, matchesTypes: string[]): string =>\n\t\t\t\tgetDataFromDataTransfer(dataTransfer, matchesTypes, ''),\n\t\t\tdataTransfer: (dataTransfer: DataTransfer): DataTransfer => dataTransfer,\n\t\t},\n\t\tmatchesTypes: ['Text', 'text/plain'],\n\t},\n}\n"], "names": ["NativeTypes", "getDataFromDataTransfer", "nativeTypesConfig", "FILE", "exposeProperties", "files", "dataTransfer", "Array", "prototype", "slice", "call", "items", "matchesTypes", "HTML", "html", "URL", "urls", "split", "TEXT", "text"], "mappings": "AAAA,YAAYA,WAAW,MAAM,mBAAmB,CAAA;AAChD,SAASC,uBAAuB,QAAQ,8BAA8B,CAAA;AActE,OAAO,MAAMC,iBAAiB,GAE1B;IACH,CAACF,WAAW,CAACG,IAAI,CAAC,EAAE;QACnBC,gBAAgB,EAAE;YACjBC,KAAK,EAAE,CAACC,YAA0B,GACjCC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACJ,YAAY,CAACD,KAAK,CAAC;YAAA;YAC/CM,KAAK,EAAE,CAACL,YAA0B,GACjCA,YAAY,CAACK,KAAK;YAAA;YACnBL,YAAY,EAAE,CAACA,YAA0B,GAAmBA,YAAY;SACxE;QACDM,YAAY,EAAE;YAAC,OAAO;SAAC;KACvB;IACD,CAACZ,WAAW,CAACa,IAAI,CAAC,EAAE;QACnBT,gBAAgB,EAAE;YACjBU,IAAI,EAAE,CAACR,YAA0B,EAAEM,YAAsB,GACxDX,uBAAuB,CAACK,YAAY,EAAEM,YAAY,EAAE,EAAE,CAAC;YAAA;YACxDN,YAAY,EAAE,CAACA,YAA0B,GAAmBA,YAAY;SACxE;QACDM,YAAY,EAAE;YAAC,MAAM;YAAE,WAAW;SAAC;KACnC;IACD,CAACZ,WAAW,CAACe,GAAG,CAAC,EAAE;QAClBX,gBAAgB,EAAE;YACjBY,IAAI,EAAE,CAACV,YAA0B,EAAEM,YAAsB,GACxDX,uBAAuB,CAACK,YAAY,EAAEM,YAAY,EAAE,EAAE,CAAC,CAACK,KAAK,CAAC,IAAI,CAAC;YAAA;YACpEX,YAAY,EAAE,CAACA,YAA0B,GAAmBA,YAAY;SACxE;QACDM,YAAY,EAAE;YAAC,KAAK;YAAE,eAAe;SAAC;KACtC;IACD,CAACZ,WAAW,CAACkB,IAAI,CAAC,EAAE;QACnBd,gBAAgB,EAAE;YACjBe,IAAI,EAAE,CAACb,YAA0B,EAAEM,YAAsB,GACxDX,uBAAuB,CAACK,YAAY,EAAEM,YAAY,EAAE,EAAE,CAAC;YAAA;YACxDN,YAAY,EAAE,CAACA,YAA0B,GAAmBA,YAAY;SACxE;QACDM,YAAY,EAAE;YAAC,MAAM;YAAE,YAAY;SAAC;KACpC;CACD,CAAA"}