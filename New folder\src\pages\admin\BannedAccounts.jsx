import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faUserSlash,
  faCalendarAlt,
  faExclamationTriangle,
  faQuestionCircle,
  faSearch,
  faFilter,
  faUndo,
  faTrash,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons'
import Header from '../../components/common/Header'
import LoadingSpinner from '../../components/common/LoadingSpinner'
import {
  useBannedAccounts,
  useUnbanUser,
  useDeleteBanRecord
} from '../../hooks/useUsers'

const BannedAccounts = () => {
  // React Query hooks
  const {
    data: bannedAccounts = [],
    isLoading: loading,
    error: queryError,
    refetch
  } = useBannedAccounts()

  const unbanUserMutation = useUnbanUser()
  const deleteBanRecordMutation = useDeleteBanRecord()

  const [searchTerm, setSearchTerm] = useState('')
  const [filterBy, setFilterBy] = useState('all')

  // Check if it's a 404 error (endpoint not implemented)
  const apiError = queryError?.response?.status === 404
    ? 'The banned accounts feature is not yet implemented in the backend.'
    : queryError
    ? 'Failed to load banned accounts. Please try again later.'
    : null

  const handleUnban = async (userId) => {
    if (!window.confirm('Are you sure you want to unban this user?')) return

    try {
      await unbanUserMutation.mutateAsync(userId)
    } catch (error) {
      // Error handling is done in the mutation
    }
  }

  const handleDeleteBanRecord = async (banId) => {
    if (!window.confirm('Are you sure you want to delete this ban record?')) return

    try {
      await deleteBanRecordMutation.mutateAsync(banId)
    } catch (error) {
      // Error handling is done in the mutation
    }
  }

  const filteredAccounts = bannedAccounts.filter(account => {
    const matchesSearch = account.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         account.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         account.reason?.toLowerCase().includes(searchTerm.toLowerCase())

    if (filterBy === 'all') return matchesSearch
    if (filterBy === 'cheating') return matchesSearch && account.reason?.toLowerCase().includes('cheat')
    if (filterBy === 'manual') return matchesSearch && !account.reason?.toLowerCase().includes('cheat')
    
    return matchesSearch
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  // Show API error if endpoint is not implemented
  if (apiError) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <FontAwesomeIcon icon={faInfoCircle} className="text-6xl text-blue-500 mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Banned Accounts Management
            </h1>
            <div className="max-w-md mx-auto bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-600 text-2xl mb-3" />
              <p className="text-gray-700 dark:text-gray-300 mb-4">
                {apiError}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                This feature will be available once the backend API endpoint is implemented.
              </p>
            </div>
            <button
              onClick={() => refetch()}
              className="mt-6 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center space-x-3">
                <FontAwesomeIcon icon={faUserSlash} className="text-red-600" />
                <span>Banned Accounts</span>
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Manage banned user accounts and view ban details
              </p>
            </div>
          </div>

          {/* Filters */}
          <div className="card">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <FontAwesomeIcon 
                    icon={faSearch} 
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
                  />
                  <input
                    type="text"
                    placeholder="Search by username, email, or reason..."
                    className="form-input pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <select
                  className="form-input"
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                >
                  <option value="all">All Bans</option>
                  <option value="cheating">Cheating Bans</option>
                  <option value="manual">Manual Bans</option>
                </select>
              </div>
            </div>
          </div>

          {/* Banned Accounts List */}
          <div className="card">
            {filteredAccounts.length === 0 ? (
              <div className="text-center py-12">
                <FontAwesomeIcon icon={faUserSlash} className="text-4xl text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No banned accounts found
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  {searchTerm || filterBy !== 'all' 
                    ? 'Try adjusting your search or filter criteria'
                    : 'No users are currently banned'
                  }
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Reason
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Ban Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Cheating Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Quiz
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredAccounts.map((account) => (
                      <tr key={account.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {account.username}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {account.email}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 dark:text-white max-w-xs">
                            {account.reason}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
                            {new Date(account.ban_date).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {account.cheating_attempts > 0 ? (
                            <div className="flex items-center text-sm text-red-600 dark:text-red-400">
                              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                              {account.cheating_attempts} attempts
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">N/A</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {account.quiz_title ? (
                            <div className="flex items-center text-sm text-gray-900 dark:text-white">
                              <FontAwesomeIcon icon={faQuestionCircle} className="mr-2" />
                              {account.quiz_title}
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">N/A</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleUnban(account.user_id)}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              title="Unban user"
                            >
                              <FontAwesomeIcon icon={faUndo} />
                            </button>
                            <button
                              onClick={() => handleDeleteBanRecord(account.id)}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                              title="Delete ban record"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="card">
              <div className="flex items-center">
                <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded-lg mr-4">
                  <FontAwesomeIcon icon={faUserSlash} className="text-red-600 text-xl" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {bannedAccounts.length}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Total Banned
                  </p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg mr-4">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="text-orange-600 text-xl" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {bannedAccounts.filter(a => a.cheating_attempts > 0).length}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Cheating Bans
                  </p>
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg mr-4">
                  <FontAwesomeIcon icon={faQuestionCircle} className="text-blue-600 text-xl" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {bannedAccounts.filter(a => a.quiz_title).length}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Quiz-Related Bans
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default BannedAccounts
