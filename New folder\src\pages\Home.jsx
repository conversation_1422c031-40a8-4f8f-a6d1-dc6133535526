import React from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faGraduationCap,
  faUsers,
  faTrophy,
  faStar,
  faCheck,
  faBook
} from '@fortawesome/free-solid-svg-icons'
import Header from '../components/common/Header'
import WavesAnimation from '../components/common/WavesAnimation'
import ImageWithFallback from '../components/common/ImageWithFallback'
import LoadingSpinner from '../components/common/LoadingSpinner'
import { useHomepageSettings } from '../hooks/useSettings'
import { useCategories } from '../hooks/useCategories'

const Home = () => {
  // Use React Query hooks for data fetching
  const {
    data: homepageSettings,
    isLoading: settingsLoading,
    error: settingsError
  } = useHomepageSettings()

  const {
    data: categories = [],
    isLoading: categoriesLoading
  } = useCategories()

  // Default settings fallback
  const defaultSettings = {
    heroTitle: 'Learn Without Limits',
    heroDescription: 'Discover thousands of courses, build skills, and advance your career with our comprehensive learning platform.',
    heroImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=600&fit=crop&crop=face',
    heroBackgroundType: 'gradient',
    heroBackgroundValue: 'from-blue-600 via-purple-600 to-indigo-700',
    heroButtonText: 'Start Learning',
    heroButtonUrl: '/courses',
    iconCardsBackgroundType: 'color',
    iconCardsBackgroundValue: '#ffffff',
    categoriesBackgroundType: 'color',
    categoriesBackgroundValue: '#f9fafb',
    iconCards: [
      {
        icon: 'faGraduationCap',
        title: 'Expert Instructors',
        description: 'Learn from industry professionals with years of experience.',
        url: '/courses'
      },
      {
        icon: 'faUsers',
        title: 'Community Learning',
        description: 'Join a vibrant community of learners and share knowledge.',
        url: '/courses'
      },
      {
        icon: 'faTrophy',
        title: 'Certificates',
        description: 'Earn recognized certificates upon course completion.',
        url: '/courses'
      }
    ],
    featuredCategories: []
  }

  // Use fetched settings or fallback to defaults
  const settings = settingsError ? defaultSettings : (homepageSettings || defaultSettings)
  const loading = settingsLoading || categoriesLoading

  const getHeroBackgroundClass = () => {
    if (!settings) return 'bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700'

    if (settings.heroBackgroundType === 'gradient') {
      return `bg-gradient-to-br ${settings.heroBackgroundValue} dark:from-blue-800 dark:via-purple-800 dark:to-indigo-900`
    }
    return ''
  }

  const getHeroBackgroundStyle = () => {
    if (!settings) return {}

    if (settings.heroBackgroundType === 'color') {
      return { backgroundColor: settings.heroBackgroundValue }
    }
    if (settings.heroBackgroundType === 'image') {
      return {
        backgroundImage: `url(${settings.heroBackgroundValue})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    }
    return {}
  }

  const getIconCardsBackgroundClass = () => {
    if (!settings) return 'bg-white dark:bg-gray-800'

    if (settings.iconCardsBackgroundType === 'gradient') {
      return `bg-gradient-to-br ${settings.iconCardsBackgroundValue}`
    }
    return 'bg-white dark:bg-gray-800'
  }

  const getIconCardsBackgroundStyle = () => {
    if (!settings) return {}

    if (settings.iconCardsBackgroundType === 'color') {
      return { backgroundColor: settings.iconCardsBackgroundValue }
    }
    if (settings.iconCardsBackgroundType === 'image') {
      return {
        backgroundImage: `url(${settings.iconCardsBackgroundValue})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    }
    return {}
  }

  const getCategoriesBackgroundClass = () => {
    if (!settings) return 'bg-gray-50 dark:bg-gray-900'

    if (settings.categoriesBackgroundType === 'gradient') {
      return `bg-gradient-to-br ${settings.categoriesBackgroundValue}`
    }
    return 'bg-gray-50 dark:bg-gray-900'
  }

  const getCategoriesBackgroundStyle = () => {
    if (!settings) return {}

    if (settings.categoriesBackgroundType === 'color') {
      return { backgroundColor: settings.categoriesBackgroundValue }
    }
    if (settings.categoriesBackgroundType === 'image') {
      return {
        backgroundImage: `url(${settings.categoriesBackgroundValue})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    }
    return {}
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      {/* Hero Section */}
      <section
        className={`relative min-h-screen flex items-center text-white overflow-hidden ${getHeroBackgroundClass()}`}
        style={getHeroBackgroundStyle()}
      >
        <div className="absolute inset-0 bg-black opacity-20"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 w-full">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Side - Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center lg:text-left"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                {settings?.heroTitle || 'Learn Without Limits'}
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-gray-100 leading-relaxed">
                {settings?.heroDescription || 'Discover thousands of courses, build skills, and advance your career with our comprehensive learning platform.'}
              </p>
              <div className="flex justify-center lg:justify-start">
                <Link
                  to={settings?.heroButtonUrl || '/courses'}
                  className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 inline-flex items-center justify-center shadow-lg"
                >
                  {settings?.heroButtonText || 'Start Learning'}
                </Link>
              </div>
            </motion.div>

            {/* Right Side - Teacher Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative">
                {/* Background Effects */}
                <div className="absolute -inset-4 bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 rounded-full blur-lg opacity-30 animate-pulse"></div>
                <div className="absolute -inset-2 bg-gradient-to-r from-blue-400 via-purple-500 to-indigo-500 rounded-full blur-md opacity-40"></div>

                {/* Teacher Image */}
                <div className="relative bg-white rounded-full p-2 shadow-2xl">
                  <ImageWithFallback
                    src={settings?.heroImage || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=600&fit=crop&crop=face'}
                    alt="Expert Teacher"
                    className="w-full h-auto rounded-full object-cover shadow-lg"
                    style={{ aspectRatio: '1/1' }}
                    fallbackText="Teacher Image"
                  />
                </div>

                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="absolute -top-4 -right-4 bg-yellow-400 rounded-full p-3 shadow-lg"
                >
                  <FontAwesomeIcon icon={faStar} className="text-white text-xl" />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="absolute -bottom-4 -left-4 bg-green-400 rounded-full p-3 shadow-lg"
                >
                  <FontAwesomeIcon icon={faCheck} className="text-white text-xl" />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Waves Animation at Bottom */}
        <WavesAnimation position="bottom" />
      </section>

      {/* Icon Cards Section */}
      <section
        className={`py-20 relative z-10 ${getIconCardsBackgroundClass()}`}
        style={getIconCardsBackgroundStyle()}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {(settings?.iconCards || []).map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group"
              >
                <Link
                  to={card.url || '/courses'}
                  className="block p-8 rounded-xl bg-gray-50 dark:bg-gray-700 hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-200 dark:border-gray-600"
                >
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
                      <FontAwesomeIcon
                        icon={card.icon === 'faGraduationCap' ? faGraduationCap :
                              card.icon === 'faUsers' ? faUsers :
                              card.icon === 'faTrophy' ? faTrophy : faGraduationCap}
                        className="text-2xl text-blue-600 dark:text-blue-400"
                      />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                      {card.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {card.description}
                    </p>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      {settings?.featuredCategories?.length > 0 && (
        <section
          className={`py-20 ${getCategoriesBackgroundClass()}`}
          style={getCategoriesBackgroundStyle()}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Explore Categories
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Choose from our wide range of course categories to start your learning journey.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {(settings?.featuredCategories || []).map((categoryId, index) => {
                const category = categories.find(c => c.id === categoryId)
                if (!category) return null

                return (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="group"
                  >
                    <Link
                      to={`/courses?category=${category.slug}`}
                      className="block bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                    >
                      <div className="aspect-video bg-gradient-to-br from-blue-400 to-purple-500 relative overflow-hidden">
                        <ImageWithFallback
                          src={category.image_url}
                          alt={category.name}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                          fallbackIcon={faBook}
                          fallbackText={category.name}
                        />
                        <div className="absolute inset-0 bg-black opacity-20 group-hover:opacity-10 transition-opacity"></div>
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                          {category.name}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300">
                          {category.courseCount || 0} courses
                        </p>
                      </div>
                    </Link>
                  </motion.div>
                )
              })}
            </div>
          </div>
        </section>
      )}

    </div>
  )
}

export default Home
