{"version": 3, "sources": ["../../src/utils/equality.ts"], "sourcesContent": ["import type { XYCoord } from '../interfaces.js'\n\nexport type EqualityCheck<T> = (a: T, b: T) => boolean\nexport const strictEquality = <T>(a: T, b: T): boolean => a === b\n\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */\nexport function areCoordsEqual(\n\toffsetA: XYCoord | null | undefined,\n\toffsetB: XYCoord | null | undefined,\n): boolean {\n\tif (!offsetA && !offsetB) {\n\t\treturn true\n\t} else if (!offsetA || !offsetB) {\n\t\treturn false\n\t} else {\n\t\treturn offsetA.x === offsetB.x && offsetA.y === offsetB.y\n\t}\n}\n\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */\nexport function areArraysEqual<T>(\n\ta: T[],\n\tb: T[],\n\tisEqual: EqualityCheck<T> = strictEquality,\n): boolean {\n\tif (a.length !== b.length) {\n\t\treturn false\n\t}\n\tfor (let i = 0; i < a.length; ++i) {\n\t\tif (!isEqual(a[i] as T, b[i] as T)) {\n\t\t\treturn false\n\t\t}\n\t}\n\treturn true\n}\n"], "names": ["strictEquality", "a", "b", "areCoordsEqual", "offsetA", "offsetB", "x", "y", "areArraysEqual", "isEqual", "length", "i"], "mappings": "AAGA,OAAO,MAAMA,cAAc,GAAG,CAAIC,CAAI,EAAEC,CAAI,GAAcD,CAAC,KAAKC,CAAC;AAAA,CAAA;AAEjE;;;;GAIG,CACH,OAAO,SAASC,cAAc,CAC7BC,OAAmC,EACnCC,OAAmC,EACzB;IACV,IAAI,CAACD,OAAO,IAAI,CAACC,OAAO,EAAE;QACzB,OAAO,IAAI,CAAA;KACX,MAAM,IAAI,CAACD,OAAO,IAAI,CAACC,OAAO,EAAE;QAChC,OAAO,KAAK,CAAA;KACZ,MAAM;QACN,OAAOD,OAAO,CAACE,CAAC,KAAKD,OAAO,CAACC,CAAC,IAAIF,OAAO,CAACG,CAAC,KAAKF,OAAO,CAACE,CAAC,CAAA;KACzD;CACD;AAED;;;;GAIG,CACH,OAAO,SAASC,cAAc,CAC7BP,CAAM,EACNC,CAAM,EACNO,OAAyB,GAAGT,cAAc,EAChC;IACV,IAAIC,CAAC,CAACS,MAAM,KAAKR,CAAC,CAACQ,MAAM,EAAE;QAC1B,OAAO,KAAK,CAAA;KACZ;IACD,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,CAAC,CAACS,MAAM,EAAE,EAAEC,CAAC,CAAE;QAClC,IAAI,CAACF,OAAO,CAACR,CAAC,CAACU,CAAC,CAAC,EAAOT,CAAC,CAACS,CAAC,CAAC,CAAM,EAAE;YACnC,OAAO,KAAK,CAAA;SACZ;KACD;IACD,OAAO,IAAI,CAAA;CACX"}