import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faList,
  faPlus,
  faCog,
  faSearch,
  faFilter,
  faDownload,
  faTrash,
  faQrcode,
  faCopy,
  faSpinner,
  faEye,
  faCalendarAlt,
  faTicketAlt,
  faWallet,
  faPrint,
  faPalette
} from '@fortawesome/free-solid-svg-icons';
import Swal from 'sweetalert2';
import Header from '../../components/common/Header';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import CardDesigner from '../../components/payment/CardDesigner';
import { useAuth } from '../../context/AuthContext';
import {
  usePaymentCodes,
  useGeneratePaymentCodes,
  useDeletePaymentCode,
  usePaymentCodeSettings
} from '../../hooks/usePayment';
import { useCourses } from '../../hooks/useCourses';

const PaymentCodes = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('list');
  const [loading, setLoading] = useState(false);
  
  // List tab state
  const [codes, setCodes] = useState([]);
  const [pagination, setPagination] = useState({});
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    level: '',
    class_id: '',
    status: '',
    page: 1,
    limit: 20
  });

  // Create tab state
  const [createForm, setCreateForm] = useState({
    type: 'course',
    courseId: '',
    walletAmount: '',
    usageLimit: 1,
    expirationType: 'never', // 'never', 'hours', 'days', 'date'
    expirationHours: '',
    expirationDays: '',
    expirationDate: '',
    codeCount: 1
  });
  const [courses, setCourses] = useState([]);
  const [creating, setCreating] = useState(false);

  // Settings tab state
  const [settings, setSettings] = useState({
    codeLength: 8,
    useNumbers: true,
    useLetters: true,
    useSymbols: false
  });

  // Download modal state
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadData, setDownloadData] = useState(null);
  const [downloadType, setDownloadType] = useState('single'); // 'single', 'all', 'filtered'

  // Card designer state
  const [savedCardDesigns, setSavedCardDesigns] = useState([]);

  // Get default card template
  const getDefaultCardTemplate = () => {
    const savedTemplates = localStorage.getItem('cardTemplates');
    const savedDefaultId = localStorage.getItem('defaultTemplateId');

    if (savedTemplates && savedDefaultId) {
      const templates = JSON.parse(savedTemplates);
      return templates.find(t => t.id === savedDefaultId);
    }
    return null;
  };

  const tabs = [
    { id: 'list', label: 'All Codes', icon: faList },
    { id: 'create', label: 'Create New', icon: faPlus },
    { id: 'designer', label: 'Card Designer', icon: faPalette },
    { id: 'settings', label: 'Settings', icon: faCog }
  ];

  useEffect(() => {
    if (user?.role !== 'admin') {
      return;
    }

    if (activeTab === 'list') {
      fetchCodes();
    } else if (activeTab === 'create') {
      fetchCourses();
    } else if (activeTab === 'settings') {
      fetchSettings();
    }
  }, [activeTab, filters, user]);

  const fetchCodes = async () => {
    setLoading(true);
    try {
      const response = await paymentCodeService.getAll(filters);
      setCodes(response.codes || []);
      setPagination(response.pagination || {});
    } catch (error) {
      console.error('Error fetching codes:', error);
      Swal.fire('Error', 'Failed to fetch payment codes', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchCourses = async () => {
    try {
      const response = await courseService.getAll({ limit: 1000 });
      setCourses(response.courses || []);
    } catch (error) {
      console.error('Error fetching courses:', error);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await paymentCodeService.getSettings();
      setSettings(response);
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filtering
    }));
  };

  const handleCreateCode = async () => {
    if (createForm.type === 'course' && !createForm.courseId) {
      Swal.fire('Error', 'Please select a course', 'error');
      return;
    }

    if (createForm.type === 'wallet' && (!createForm.walletAmount || createForm.walletAmount <= 0)) {
      Swal.fire('Error', 'Please enter a valid wallet amount', 'error');
      return;
    }

    if (createForm.codeCount > 50) {
      Swal.fire('Error', 'Maximum 50 codes can be generated at once', 'error');
      return;
    }

    setCreating(true);
    try {
      const response = await paymentCodeService.generate({
        ...createForm,
        ...settings
      });

      Swal.fire({
        icon: 'success',
        title: 'Codes Generated!',
        text: `${createForm.codeCount} codes generated successfully`,
        showCancelButton: true,
        confirmButtonText: 'Download Codes',
        cancelButtonText: 'Close'
      }).then((result) => {
        if (result.isConfirmed) {
          openDownloadModal(response.codes, 'generated');
        }
      });

      // Reset form
      setCreateForm({
        type: 'course',
        courseId: '',
        walletAmount: '',
        usageLimit: 1,
        expirationType: 'never',
        expirationHours: '',
        expirationDays: '',
        expirationDate: '',
        codeCount: 1
      });

      // Refresh codes list if on list tab
      if (activeTab === 'list') {
        fetchCodes();
      }

    } catch (error) {
      console.error('Error creating codes:', error);
      Swal.fire('Error', error.message || 'Failed to generate codes', 'error');
    } finally {
      setCreating(false);
    }
  };

  const openDownloadModal = (data, type) => {
    setDownloadData(data);
    setDownloadType(type);
    setShowDownloadModal(true);
  };

  const downloadAsText = (data, type) => {
    let content = '';
    let filename = '';

    if (type === 'single') {
      const code = data;
      content = `Code: ${code.code}\nType: ${code.type}\nValue: ${code.type === 'course' ? code.course_title : '$' + code.wallet_amount}\nExpires: ${code.expires_at ? new Date(code.expires_at).toLocaleString() : 'Never'}\nUsage Limit: ${code.usage_limit || 'Unlimited'}\nQR Code URL: ${paymentCodeService.generateQRCode(code.code)}`;
      filename = `payment-code-${code.code}.txt`;
    } else {
      const codesList = data;
      content = codesList.map(code =>
        `Code: ${code.code}\nType: ${code.type}\nValue: ${code.type === 'course' ? code.course_title : '$' + code.wallet_amount}\nExpires: ${code.expires_at ? new Date(code.expires_at).toLocaleString() : 'Never'}\nUsage Limit: ${code.usage_limit || 'Unlimited'}\nQR Code URL: ${paymentCodeService.generateQRCode(code.code)}\n---`
      ).join('\n\n');
      filename = `payment-codes-${Date.now()}.txt`;
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadAsPDF = async (data, type) => {
    try {
      // Create a new window for PDF generation
      const printWindow = window.open('', '_blank');

      let htmlContent = '';
      let title = '';

      if (type === 'single') {
        const code = data;
        title = `Payment Code: ${code.code}`;
        htmlContent = `
          <div style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
            <h1>Payment Code</h1>
            <div style="margin: 20px 0;">
              <img src="${paymentCodeService.generateQRCode(code.code)}" alt="QR Code" style="width: 200px; height: 200px;" />
            </div>
            <div style="margin: 20px 0; font-size: 18px;">
              <strong>Code: ${code.code}</strong>
            </div>
            <div style="margin: 10px 0;">
              <strong>Type:</strong> ${code.type === 'course' ? 'Course Purchase' : 'Wallet Recharge'}
            </div>
            <div style="margin: 10px 0;">
              <strong>Value:</strong> ${code.type === 'course' ? code.course_title : '$' + code.wallet_amount}
            </div>
            <div style="margin: 10px 0;">
              <strong>Expires:</strong> ${code.expires_at ? new Date(code.expires_at).toLocaleString() : 'Never'}
            </div>
            <div style="margin: 10px 0;">
              <strong>Usage Limit:</strong> ${code.usage_limit || 'Unlimited'}
            </div>
            <div style="margin: 20px 0; font-size: 12px; color: #666;">
              Scan the QR code or enter the code manually to redeem
            </div>
          </div>
        `;
      } else {
        const codesList = data;
        title = `Payment Codes (${codesList.length} codes)`;
        htmlContent = `
          <div style="padding: 20px; font-family: Arial, sans-serif;">
            <h1 style="text-align: center;">Payment Codes</h1>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px;">
              ${codesList.map(code => `
                <div style="border: 1px solid #ddd; padding: 15px; text-align: center; page-break-inside: avoid;">
                  <img src="${paymentCodeService.generateQRCode(code.code)}" alt="QR Code" style="width: 150px; height: 150px;" />
                  <div style="margin: 10px 0; font-weight: bold; font-size: 14px;">${code.code}</div>
                  <div style="font-size: 12px; color: #666;">
                    ${code.type === 'course' ? code.course_title : '$' + code.wallet_amount}<br/>
                    ${code.expires_at ? 'Expires: ' + new Date(code.expires_at).toLocaleDateString() : 'No expiration'}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        `;
      }

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${title}</title>
          <style>
            @media print {
              body { margin: 0; }
              @page { margin: 1cm; }
            }
          </style>
        </head>
        <body>
          ${htmlContent}
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                window.close();
              }, 1000);
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();
    } catch (error) {
      console.error('Error generating PDF:', error);
      Swal.fire('Error', 'Failed to generate PDF. Please try again.', 'error');
    }
  };

  const handleDownloadConfirm = async (format) => {
    setShowDownloadModal(false);

    if (format === 'text') {
      downloadAsText(downloadData, downloadType);
    } else if (format === 'pdf') {
      await downloadAsPDF(downloadData, downloadType);
    }

    setDownloadData(null);
    setDownloadType('single');
  };

  const printCode = (code) => {
    const defaultTemplate = getDefaultCardTemplate();

    if (!defaultTemplate) {
      // No default template, show message and suggest creating one
      Swal.fire({
        icon: 'info',
        title: 'No Default Card Template',
        html: `
          <div class="text-left">
            <p class="mb-3">To print payment codes with custom designs, you need to:</p>
            <ol class="list-decimal list-inside space-y-2 text-sm">
              <li>Go to the <strong>Card Designer</strong> tab</li>
              <li>Create a new card template</li>
              <li>Add placeholders like QR code, code text, etc.</li>
              <li>Save the template</li>
              <li>Set it as <strong>default</strong></li>
            </ol>
            <p class="mt-3 text-sm text-gray-600">Or use simple print for basic output.</p>
          </div>
        `,
        confirmButtonText: 'Go to Card Designer',
        showCancelButton: true,
        cancelButtonText: 'Use Simple Print',
        customClass: {
          popup: 'text-left'
        }
      }).then((result) => {
        if (result.isConfirmed) {
          setActiveTab('designer');
        } else {
          // Fallback to simple PDF
          downloadAsPDF(code, 'single');
        }
      });
      return;
    }

    // Use the default template to print the card
    printWithTemplate(code, defaultTemplate);
  };

  const printWithTemplate = (code, template) => {
    // Prepare code data for template
    const codeData = {
      qrcode: paymentCodeService.generateQRCode(code.code),
      code: code.code,
      type: code.type === 'course' ? 'Course Purchase' : 'Wallet Recharge',
      value: code.type === 'course' ? code.course?.title || 'Course' : `$${code.value}`,
      expiry: code.expiresAt ? new Date(code.expiresAt).toLocaleDateString() : 'No Expiry',
      usage: code.usageLimit ? `${code.usageCount}/${code.usageLimit} uses` : 'Unlimited'
    };

    // Create print window
    const printWindow = window.open('', '_blank');

    const generateCardHTML = (cardDesign, side) => {
      const renderElement = (element) => {
        if (element.type === 'placeholder') {
          const value = codeData[element.id] || `{${element.id}}`;

          if (element.id === 'qrcode') {
            return `<img src="${value}" style="width: 100%; height: 100%; object-fit: contain;" />`;
          }

          return `<div style="
            font-size: ${element.fontSize || 14}px;
            font-family: ${element.fontFamily || 'Arial'};
            color: ${element.color || '#000000'};
            font-weight: ${element.fontWeight || 'normal'};
            font-style: ${element.fontStyle || 'normal'};
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          ">${value}</div>`;
        } else if (element.type === 'text') {
          return `<div style="
            font-size: ${element.fontSize}px;
            font-family: ${element.fontFamily};
            color: ${element.color};
            font-weight: ${element.fontWeight};
            font-style: ${element.fontStyle};
          ">${element.content}</div>`;
        } else if (element.type === 'image') {
          return `<img src="${element.src}" style="width: 100%; height: 100%; object-fit: cover;" />`;
        } else if (element.type === 'shape') {
          // Handle shapes
          if (element.shapeType === 'rectangle') {
            return `<div style="
              width: 100%;
              height: 100%;
              background-color: ${element.fill};
              border: ${element.strokeWidth}px solid ${element.stroke};
              border-radius: 4px;
            "></div>`;
          } else if (element.shapeType === 'circle') {
            return `<div style="
              width: 100%;
              height: 100%;
              background-color: ${element.fill};
              border: ${element.strokeWidth}px solid ${element.stroke};
              border-radius: 50%;
            "></div>`;
          }
        }
        return '';
      };

      return `
        <div style="
          width: 400px;
          height: 250px;
          background-color: ${cardDesign.background};
          background-image: ${cardDesign.backgroundImage ? `url(${cardDesign.backgroundImage})` : 'none'};
          background-size: cover;
          background-position: center;
          position: relative;
          margin: 20px auto;
          border: 1px solid #ddd;
          page-break-inside: avoid;
        ">
          ${cardDesign.elements.map(element => `
            <div style="
              position: absolute;
              left: ${element.position.x}px;
              top: ${element.position.y}px;
              width: ${element.size.width}px;
              height: ${element.size.height}px;
              transform: rotate(${element.rotation || 0}deg);
              z-index: ${element.zIndex};
            ">
              ${renderElement(element)}
            </div>
          `).join('')}
        </div>
      `;
    };

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Payment Code Card - ${code.code}</title>
        <style>
          @media print {
            body { margin: 0; }
            @page { margin: 1cm; }
          }
          body { font-family: Arial, sans-serif; }
        </style>
      </head>
      <body>
        <h1 style="text-align: center;">Payment Code Card</h1>
        <p style="text-align: center;">Code: ${code.code}</p>
        ${generateCardHTML(template.design.front, 'Front')}
        ${generateCardHTML(template.design.back, 'Back')}
        <script>
          window.onload = function() {
            setTimeout(function() {
              window.print();
              window.close();
            }, 1000);
          };
        </script>
      </body>
      </html>
    `);

    printWindow.document.close();

    // Show success notification
    Swal.fire({
      icon: 'success',
      title: 'Print Ready!',
      text: `Payment code card for "${code.code}" is ready to print using template "${template.name}".`,
      timer: 3000,
      showConfirmButton: false,
      toast: true,
      position: 'top-end'
    });
  };

  const copyCode = (code) => {
    navigator.clipboard.writeText(code);
    Swal.fire({
      icon: 'success',
      title: 'Copied!',
      text: 'Code copied to clipboard',
      timer: 1500,
      showConfirmButton: false
    });
  };

  const deleteCode = async (id) => {
    const result = await Swal.fire({
      title: 'Delete Code?',
      text: 'This action cannot be undone',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Delete'
    });

    if (result.isConfirmed) {
      try {
        await paymentCodeService.delete(id);
        Swal.fire('Deleted!', 'Code has been deleted', 'success');
        fetchCodes();
      } catch (error) {
        Swal.fire('Error', 'Failed to delete code', 'error');
      }
    }
  };

  const deleteAllCodes = async () => {
    const result = await Swal.fire({
      title: 'Delete All Codes?',
      text: 'This will delete ALL payment codes. This action cannot be undone!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Delete All',
      input: 'text',
      inputPlaceholder: 'Type "DELETE ALL" to confirm',
      inputValidator: (value) => {
        if (value !== 'DELETE ALL') {
          return 'Please type "DELETE ALL" to confirm';
        }
      }
    });

    if (result.isConfirmed) {
      try {
        await paymentCodeService.deleteAll();
        Swal.fire('Deleted!', 'All codes have been deleted', 'success');
        fetchCodes();
      } catch (error) {
        Swal.fire('Error', 'Failed to delete codes', 'error');
      }
    }
  };

  const deleteUsedExpiredCodes = async () => {
    const result = await Swal.fire({
      title: 'Delete Used & Expired Codes?',
      text: 'This will delete all used and expired payment codes',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Delete'
    });

    if (result.isConfirmed) {
      try {
        await paymentCodeService.deleteUsedAndExpired();
        Swal.fire('Deleted!', 'Used and expired codes have been deleted', 'success');
        fetchCodes();
      } catch (error) {
        Swal.fire('Error', 'Failed to delete codes', 'error');
      }
    }
  };

  if (user?.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600 dark:text-gray-400">
              You don't have permission to access this page.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Payment Codes Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Create and manage payment codes for courses and wallet recharge
          </p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <FontAwesomeIcon icon={tab.icon} className="mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'list' && (
              <div>
                {/* Filters and Actions */}
                <div className="card mb-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    {/* Search and Filters */}
                    <div className="flex flex-col sm:flex-row gap-4 flex-1">
                      <div className="relative">
                        <FontAwesomeIcon
                          icon={faSearch}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <input
                          type="text"
                          placeholder="Search by code or course..."
                          value={filters.search}
                          onChange={(e) => handleFilterChange('search', e.target.value)}
                          className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        />
                      </div>

                      <select
                        value={filters.status}
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="used">Used</option>
                        <option value="expired">Expired</option>
                      </select>

                      <select
                        value={filters.limit}
                        onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      >
                        <option value={10}>10 per page</option>
                        <option value={20}>20 per page</option>
                        <option value={50}>50 per page</option>
                        <option value={100}>100 per page</option>
                      </select>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={deleteUsedExpiredCodes}
                        className="btn-secondary text-sm"
                      >
                        <FontAwesomeIcon icon={faTrash} className="mr-2" />
                        Delete Used/Expired
                      </button>
                      <button
                        onClick={deleteAllCodes}
                        className="btn-danger text-sm"
                      >
                        <FontAwesomeIcon icon={faTrash} className="mr-2" />
                        Delete All
                      </button>
                      <button
                        onClick={() => openDownloadModal(codes, 'all')}
                        className="btn-primary text-sm"
                        disabled={codes.length === 0}
                      >
                        <FontAwesomeIcon icon={faDownload} className="mr-2" />
                        Download All
                      </button>
                    </div>
                  </div>
                </div>

                {/* Codes List */}
                {loading ? (
                  <div className="text-center py-8">
                    <LoadingSpinner />
                  </div>
                ) : codes.length > 0 ? (
                  <div className="space-y-4">
                    {codes.map((code, index) => (
                      <motion.div
                        key={code.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * index }}
                        className="card"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            {/* QR Code */}
                            <div className="flex-shrink-0">
                              <img
                                src={paymentCodeService.generateQRCode(code.code)}
                                alt="QR Code"
                                className="w-16 h-16 border border-gray-200 dark:border-gray-600 rounded"
                              />
                            </div>

                            {/* Code Info */}
                            <div>
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="font-mono text-lg font-semibold text-gray-900 dark:text-white">
                                  {code.code}
                                </span>
                                <button
                                  onClick={() => copyCode(code.code)}
                                  className="text-gray-400 hover:text-primary-600 dark:hover:text-primary-400"
                                >
                                  <FontAwesomeIcon icon={faCopy} />
                                </button>
                              </div>

                              <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                                <span className="flex items-center">
                                  <FontAwesomeIcon
                                    icon={code.type === 'course' ? faTicketAlt : faWallet}
                                    className="mr-1"
                                  />
                                  {code.type === 'course' ? code.course_title : `$${code.wallet_amount} Wallet`}
                                </span>

                                <span>
                                  Usage: {code.usage_count}/{code.usage_limit === 0 ? '∞' : code.usage_limit}
                                </span>

                                {code.expires_at && (
                                  <span className="flex items-center">
                                    <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                                    Expires: {new Date(code.expires_at).toLocaleDateString()}
                                  </span>
                                )}
                              </div>

                              <div className="mt-1">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  code.status === 'active'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                                    : code.status === 'used'
                                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                }`}>
                                  {code.status}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => {
                                const qrUrl = paymentCodeService.generateQRCode(code.code);
                                window.open(qrUrl, '_blank');
                              }}
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                              title="View QR Code"
                            >
                              <FontAwesomeIcon icon={faQrcode} />
                            </button>
                            <button
                              onClick={() => printCode(code)}
                              className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300"
                              title="Print Code"
                            >
                              <FontAwesomeIcon icon={faPrint} />
                            </button>
                            <button
                              onClick={() => openDownloadModal(code, 'single')}
                              className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                              title="Download Code"
                            >
                              <FontAwesomeIcon icon={faDownload} />
                            </button>
                            <button
                              onClick={() => deleteCode(code.id)}
                              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                              title="Delete Code"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FontAwesomeIcon
                      icon={faTicketAlt}
                      className="text-4xl text-gray-400 mb-4"
                    />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      No payment codes found
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Create your first payment code using the "Create New" tab.
                    </p>
                  </div>
                )}

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="flex justify-center mt-6">
                    <div className="flex space-x-2">
                      {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          onClick={() => handleFilterChange('page', page)}
                          className={`px-3 py-2 rounded-lg ${
                            pagination.page === page
                              ? 'bg-primary-600 text-white'
                              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'create' && (
              <div className="max-w-2xl mx-auto">
                <div className="card">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Create New Payment Codes
                  </h2>

                  <div className="space-y-6">
                    {/* Code Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Code Type
                      </label>
                      <div className="grid grid-cols-2 gap-4">
                        <button
                          onClick={() => setCreateForm(prev => ({ ...prev, type: 'course' }))}
                          className={`p-4 border-2 rounded-lg text-center ${
                            createForm.type === 'course'
                              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                          }`}
                        >
                          <FontAwesomeIcon icon={faTicketAlt} className="text-2xl mb-2" />
                          <div className="font-medium">Course Purchase</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            Allow users to purchase specific courses
                          </div>
                        </button>
                        <button
                          onClick={() => setCreateForm(prev => ({ ...prev, type: 'wallet' }))}
                          className={`p-4 border-2 rounded-lg text-center ${
                            createForm.type === 'wallet'
                              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                          }`}
                        >
                          <FontAwesomeIcon icon={faWallet} className="text-2xl mb-2" />
                          <div className="font-medium">Wallet Recharge</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            Add money to user wallets
                          </div>
                        </button>
                      </div>
                    </div>

                    {/* Course Selection (for course type) */}
                    {createForm.type === 'course' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Select Course
                        </label>
                        <select
                          value={createForm.courseId}
                          onChange={(e) => setCreateForm(prev => ({ ...prev, courseId: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        >
                          <option value="">Select a course...</option>
                          {courses.map(course => (
                            <option key={course.id} value={course.id}>
                              {course.title} - ${course.price}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}

                    {/* Wallet Amount (for wallet type) */}
                    {createForm.type === 'wallet' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Wallet Amount ($)
                        </label>
                        <input
                          type="number"
                          min="0.01"
                          step="0.01"
                          value={createForm.walletAmount}
                          onChange={(e) => setCreateForm(prev => ({ ...prev, walletAmount: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="Enter amount"
                        />
                      </div>
                    )}

                    {/* Usage Limit */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Usage Limit
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={createForm.usageLimit}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, usageLimit: parseInt(e.target.value) || 1 }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        placeholder="1 = single use, 0 = unlimited"
                      />
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Set to 0 for unlimited usage by any account
                      </p>
                    </div>

                    {/* Expiration */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                        Expiration
                      </label>

                      {/* Expiration Type Selection */}
                      <div className="space-y-3 mb-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="expirationType"
                            value="never"
                            checked={createForm.expirationType === 'never'}
                            onChange={(e) => setCreateForm(prev => ({
                              ...prev,
                              expirationType: e.target.value,
                              expirationHours: '',
                              expirationDays: '',
                              expirationDate: ''
                            }))}
                            className="mr-2"
                          />
                          <span className="text-gray-700 dark:text-gray-300">Never expires</span>
                        </label>

                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="expirationType"
                            value="hours"
                            checked={createForm.expirationType === 'hours'}
                            onChange={(e) => setCreateForm(prev => ({
                              ...prev,
                              expirationType: e.target.value,
                              expirationDays: '',
                              expirationDate: ''
                            }))}
                            className="mr-2"
                          />
                          <span className="text-gray-700 dark:text-gray-300">Expires in hours</span>
                        </label>

                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="expirationType"
                            value="days"
                            checked={createForm.expirationType === 'days'}
                            onChange={(e) => setCreateForm(prev => ({
                              ...prev,
                              expirationType: e.target.value,
                              expirationHours: '',
                              expirationDate: ''
                            }))}
                            className="mr-2"
                          />
                          <span className="text-gray-700 dark:text-gray-300">Expires in days</span>
                        </label>

                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="expirationType"
                            value="date"
                            checked={createForm.expirationType === 'date'}
                            onChange={(e) => setCreateForm(prev => ({
                              ...prev,
                              expirationType: e.target.value,
                              expirationHours: '',
                              expirationDays: ''
                            }))}
                            className="mr-2"
                          />
                          <span className="text-gray-700 dark:text-gray-300">Expires on specific date</span>
                        </label>
                      </div>

                      {/* Conditional Input Fields */}
                      {createForm.expirationType === 'hours' && (
                        <div>
                          <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                            Hours from now
                          </label>
                          <input
                            type="number"
                            min="1"
                            value={createForm.expirationHours}
                            onChange={(e) => setCreateForm(prev => ({
                              ...prev,
                              expirationHours: e.target.value
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                            placeholder="24"
                          />
                        </div>
                      )}

                      {createForm.expirationType === 'days' && (
                        <div>
                          <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                            Days from now
                          </label>
                          <input
                            type="number"
                            min="1"
                            value={createForm.expirationDays}
                            onChange={(e) => setCreateForm(prev => ({
                              ...prev,
                              expirationDays: e.target.value
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                            placeholder="7"
                          />
                        </div>
                      )}

                      {createForm.expirationType === 'date' && (
                        <div>
                          <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                            Expiration date and time
                          </label>
                          <input
                            type="datetime-local"
                            value={createForm.expirationDate}
                            onChange={(e) => setCreateForm(prev => ({
                              ...prev,
                              expirationDate: e.target.value
                            }))}
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          />
                        </div>
                      )}
                    </div>

                    {/* Number of Codes */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Number of Codes to Generate
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="50"
                        value={createForm.codeCount}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, codeCount: parseInt(e.target.value) || 1 }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      />
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Maximum 50 codes per batch
                      </p>
                    </div>

                    {/* Generate Button */}
                    <div className="pt-4">
                      <button
                        onClick={handleCreateCode}
                        disabled={creating}
                        className="w-full btn-primary"
                      >
                        {creating ? (
                          <>
                            <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                            Generating Codes...
                          </>
                        ) : (
                          <>
                            <FontAwesomeIcon icon={faPlus} className="mr-2" />
                            Generate {createForm.codeCount} Code{createForm.codeCount > 1 ? 's' : ''}
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'designer' && (
              <div className="h-screen -mt-8">
                <CardDesigner />
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="max-w-lg mx-auto">
                <div className="card">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Code Generation Settings
                  </h2>

                  <div className="space-y-6">
                    {/* Code Length */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Code Length
                      </label>
                      <input
                        type="number"
                        min="4"
                        max="20"
                        value={settings.codeLength}
                        onChange={(e) => setSettings(prev => ({ ...prev, codeLength: parseInt(e.target.value) || 8 }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      />
                    </div>

                    {/* Character Types */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                        Character Types
                      </label>
                      <div className="space-y-3">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={settings.useNumbers}
                            onChange={(e) => setSettings(prev => ({ ...prev, useNumbers: e.target.checked }))}
                            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Numbers (0-9)
                          </span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={settings.useLetters}
                            onChange={(e) => setSettings(prev => ({ ...prev, useLetters: e.target.checked }))}
                            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Letters (A-Z)
                          </span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={settings.useSymbols}
                            onChange={(e) => setSettings(prev => ({ ...prev, useSymbols: e.target.checked }))}
                            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Symbols (!@#$%^&*)
                          </span>
                        </label>
                      </div>
                    </div>

                    {/* Preview */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Preview
                      </label>
                      <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <span className="font-mono text-lg">
                          {Array.from({ length: settings.codeLength }, (_, i) => {
                            if (settings.useNumbers && settings.useLetters && settings.useSymbols) return 'A1@';
                            if (settings.useNumbers && settings.useLetters) return 'A1';
                            if (settings.useNumbers && settings.useSymbols) return '1@';
                            if (settings.useLetters && settings.useSymbols) return 'A@';
                            if (settings.useNumbers) return '1';
                            if (settings.useLetters) return 'A';
                            if (settings.useSymbols) return '@';
                            return 'X';
                          }).join('').substring(0, settings.codeLength)}
                        </span>
                      </div>
                    </div>

                    {/* Save Button */}
                    <div className="pt-4">
                      <button
                        onClick={() => {
                          Swal.fire({
                            icon: 'success',
                            title: 'Settings Saved!',
                            text: 'Code generation settings have been updated.',
                            timer: 2000,
                            showConfirmButton: false
                          });
                        }}
                        className="w-full btn-primary"
                      >
                        <FontAwesomeIcon icon={faCog} className="mr-2" />
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Download Format Selection Modal */}
        <AnimatePresence>
          {showDownloadModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
              onClick={() => setShowDownloadModal(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Choose Download Format
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    How would you like to download the payment code{downloadType === 'single' ? '' : 's'}?
                  </p>

                  <div className="space-y-3">
                    <button
                      onClick={() => handleDownloadConfirm('text')}
                      className="w-full p-4 border-2 border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors text-left"
                    >
                      <div className="flex items-center space-x-3">
                        <FontAwesomeIcon icon={faDownload} className="text-blue-600 text-xl" />
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            Text File (.txt)
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            Download codes with details in a text file
                          </div>
                        </div>
                      </div>
                    </button>

                    <button
                      onClick={() => handleDownloadConfirm('pdf')}
                      className="w-full p-4 border-2 border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors text-left"
                    >
                      <div className="flex items-center space-x-3">
                        <FontAwesomeIcon icon={faQrcode} className="text-green-600 text-xl" />
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            PDF with QR Codes
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            Download printable PDF with QR codes
                          </div>
                        </div>
                      </div>
                    </button>
                  </div>

                  <div className="mt-6 flex justify-end">
                    <button
                      onClick={() => setShowDownloadModal(false)}
                      className="btn-secondary"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default PaymentCodes;
