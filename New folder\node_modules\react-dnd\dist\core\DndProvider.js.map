{"version": 3, "sources": ["../../src/core/DndProvider.tsx"], "sourcesContent": ["import type { BackendFactory, DragDropManager } from 'dnd-core'\nimport { createDragDropManager } from 'dnd-core'\nimport type { FC, ReactNode } from 'react'\nimport { memo, useEffect } from 'react'\n\nimport { DndContext } from './DndContext.js'\n\nexport type DndProviderProps<BackendContext, BackendOptions> =\n\t| {\n\t\t\tchildren?: ReactNode\n\t\t\tmanager: DragDropManager\n\t  }\n\t| {\n\t\t\tbackend: BackendFactory\n\t\t\tchildren?: ReactNode\n\t\t\tcontext?: BackendContext\n\t\t\toptions?: BackendOptions\n\t\t\tdebugMode?: boolean\n\t  }\n\nlet refCount = 0\nconst INSTANCE_SYM = Symbol.for('__REACT_DND_CONTEXT_INSTANCE__')\n\n/**\n * A React component that provides the React-DnD context\n */\nexport const DndProvider: FC<DndProviderProps<unknown, unknown>> = memo(\n\tfunction DndProvider({ children, ...props }) {\n\t\tconst [manager, isGlobalInstance] = getDndContextValue(props) // memoized from props\n\t\t/**\n\t\t * If the global context was used to store the DND context\n\t\t * then where theres no more references to it we should\n\t\t * clean it up to avoid memory leaks\n\t\t */\n\t\tuseEffect(() => {\n\t\t\tif (isGlobalInstance) {\n\t\t\t\tconst context = getGlobalContext()\n\t\t\t\t++refCount\n\n\t\t\t\treturn () => {\n\t\t\t\t\tif (--refCount === 0) {\n\t\t\t\t\t\tcontext[INSTANCE_SYM] = null\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn\n\t\t}, [])\n\n\t\treturn <DndContext.Provider value={manager}>{children}</DndContext.Provider>\n\t},\n)\n\nfunction getDndContextValue(props: DndProviderProps<unknown, unknown>) {\n\tif ('manager' in props) {\n\t\tconst manager = { dragDropManager: props.manager }\n\t\treturn [manager, false]\n\t}\n\n\tconst manager = createSingletonDndContext(\n\t\tprops.backend,\n\t\tprops.context,\n\t\tprops.options,\n\t\tprops.debugMode,\n\t)\n\tconst isGlobalInstance = !props.context\n\n\treturn [manager, isGlobalInstance]\n}\n\nfunction createSingletonDndContext<BackendContext, BackendOptions>(\n\tbackend: BackendFactory,\n\tcontext: BackendContext = getGlobalContext(),\n\toptions: BackendOptions,\n\tdebugMode?: boolean,\n) {\n\tconst ctx = context as any\n\tif (!ctx[INSTANCE_SYM]) {\n\t\tctx[INSTANCE_SYM] = {\n\t\t\tdragDropManager: createDragDropManager(\n\t\t\t\tbackend,\n\t\t\t\tcontext,\n\t\t\t\toptions,\n\t\t\t\tdebugMode,\n\t\t\t),\n\t\t}\n\t}\n\treturn ctx[INSTANCE_SYM]\n}\n\ndeclare const global: any\nfunction getGlobalContext() {\n\treturn typeof global !== 'undefined' ? global : (window as any)\n}\n"], "names": ["createDragDropManager", "memo", "useEffect", "DndContext", "refCount", "INSTANCE_SYM", "Symbol", "for", "DndProvider", "children", "props", "manager", "isGlobalInstance", "getDndContextValue", "context", "getGlobalContext", "Provider", "value", "dragDropManager", "createSingletonDndContext", "backend", "options", "debugMode", "ctx", "global", "window"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAASA,qBAAqB,QAAQ,UAAU,CAAA;AAEhD,SAASC,IAAI,EAAEC,SAAS,QAAQ,OAAO,CAAA;AAEvC,SAASC,UAAU,QAAQ,iBAAiB,CAAA;AAe5C,IAAIC,QAAQ,GAAG,CAAC;AAChB,MAAMC,YAAY,GAAGC,MAAM,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAKpDC,WAAW,iBAA2CP,IAAI,CACtE,SAASO,WAAW,CAAC,MAAsB,EAAE;QAAxB,EAAEC,QAAQ,CAAA,EAAY,GAAtB,MAAsB,EAAPC,KAAK,4BAApB,MAAsB;QAApBD,UAAQ;;IAC9B,MAAM,CAACE,OAAO,EAAEC,gBAAgB,CAAC,GAAGC,kBAAkB,CAACH,KAAK,CAAC,CAAC,sBAAsB;IAAvB;IAC7D;;;;KAIG,CACHR,SAAS,CAAC,IAAM;QACf,IAAIU,gBAAgB,EAAE;YACrB,MAAME,OAAO,GAAGC,gBAAgB,EAAE;YAClC,EAAEX,QAAQ;YAEV,OAAO,IAAM;gBACZ,IAAI,EAAEA,QAAQ,KAAK,CAAC,EAAE;oBACrBU,OAAO,CAACT,YAAY,CAAC,GAAG,IAAI;iBAC5B;aACD,CAAA;SACD;QACD,OAAM;KACN,EAAE,EAAE,CAAC;IAEN,qBAAO,KAACF,UAAU,CAACa,QAAQ;QAACC,KAAK,EAAEN,OAAO;kBAAGF,QAAQ;MAAuB,CAAA;CAC5E,CACD;AA3BD;;GAEG,CACH,yBAwBC;AAED,SAASI,kBAAkB,CAACH,KAAyC,EAAE;IACtE,IAAI,SAAS,IAAIA,KAAK,EAAE;QACvB,MAAMC,OAAO,GAAG;YAAEO,eAAe,EAAER,KAAK,CAACC,OAAO;SAAE;QAClD,OAAO;YAACA,OAAO;YAAE,KAAK;SAAC,CAAA;KACvB;IAED,MAAMA,OAAO,GAAGQ,yBAAyB,CACxCT,KAAK,CAACU,OAAO,EACbV,KAAK,CAACI,OAAO,EACbJ,KAAK,CAACW,OAAO,EACbX,KAAK,CAACY,SAAS,CACf;IACD,MAAMV,gBAAgB,GAAG,CAACF,KAAK,CAACI,OAAO;IAEvC,OAAO;QAACH,OAAO;QAAEC,gBAAgB;KAAC,CAAA;CAClC;AAED,SAASO,yBAAyB,CACjCC,OAAuB,EACvBN,OAAuB,GAAGC,gBAAgB,EAAE,EAC5CM,OAAuB,EACvBC,SAAmB,EAClB;IACD,MAAMC,GAAG,GAAGT,OAAO,AAAO;IAC1B,IAAI,CAACS,GAAG,CAAClB,YAAY,CAAC,EAAE;QACvBkB,GAAG,CAAClB,YAAY,CAAC,GAAG;YACnBa,eAAe,EAAElB,qBAAqB,CACrCoB,OAAO,EACPN,OAAO,EACPO,OAAO,EACPC,SAAS,CACT;SACD;KACD;IACD,OAAOC,GAAG,CAAClB,YAAY,CAAC,CAAA;CACxB;AAGD,SAASU,gBAAgB,GAAG;IAC3B,OAAO,OAAOS,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAIC,MAAM,AAAQ,CAAA;CAC/D"}