{"version": 3, "sources": ["../../src/reducers/dirtyHandlerIds.ts"], "sourcesContent": ["import {\n\tB<PERSON>IN_DRAG,\n\tDROP,\n\t<PERSON><PERSON>_DRAG,\n\tHOVER,\n\tPUBLISH_DRAG_SOURCE,\n} from '../actions/dragDrop/index.js'\nimport {\n\tADD_SOURCE,\n\tADD_TARGET,\n\tREMOVE_SOURCE,\n\tREMOVE_TARGET,\n} from '../actions/registry.js'\nimport type { Action } from '../interfaces.js'\nimport { ALL, NONE } from '../utils/dirtiness.js'\nimport { areArraysEqual } from '../utils/equality.js'\nimport { xor } from '../utils/js_utils.js'\n\nexport type State = string[]\n\nexport interface DirtyHandlerIdPayload {\n\ttargetIds: string[]\n\tprevTargetIds: string[]\n}\n\nexport function reduce(\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\t_state: State = NONE,\n\taction: Action<DirtyHandlerIdPayload>,\n): State {\n\tswitch (action.type) {\n\t\tcase HOVER:\n\t\t\tbreak\n\t\tcase ADD_SOURCE:\n\t\tcase ADD_TARGET:\n\t\tcase REMOVE_TARGET:\n\t\tcase REMOVE_SOURCE:\n\t\t\treturn NONE\n\t\tcase BEGIN_DRAG:\n\t\tcase PUBLISH_DRAG_SOURCE:\n\t\tcase END_DRAG:\n\t\tcase DROP:\n\t\tdefault:\n\t\t\treturn ALL\n\t}\n\n\tconst { targetIds = [], prevTargetIds = [] } = action.payload\n\tconst result = xor(targetIds, prevTargetIds)\n\tconst didChange =\n\t\tresult.length > 0 || !areArraysEqual(targetIds, prevTargetIds)\n\n\tif (!didChange) {\n\t\treturn NONE\n\t}\n\n\t// Check the target ids at the innermost position. If they are valid, add them\n\t// to the result\n\tconst prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1]\n\tconst innermostTargetId = targetIds[targetIds.length - 1]\n\tif (prevInnermostTargetId !== innermostTargetId) {\n\t\tif (prevInnermostTargetId) {\n\t\t\tresult.push(prevInnermostTargetId)\n\t\t}\n\t\tif (innermostTargetId) {\n\t\t\tresult.push(innermostTargetId)\n\t\t}\n\t}\n\n\treturn result\n}\n"], "names": ["BEGIN_DRAG", "DROP", "END_DRAG", "HOVER", "PUBLISH_DRAG_SOURCE", "ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "ALL", "NONE", "areArraysEqual", "xor", "reduce", "_state", "action", "type", "targetIds", "prevTargetIds", "payload", "result", "<PERSON><PERSON><PERSON><PERSON>", "length", "prevInnermostTargetId", "innermostTargetId", "push"], "mappings": "AAAA,SACCA,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,mBAAmB,QACb,8BAA8B,CAAA;AACrC,SACCC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,aAAa,QACP,wBAAwB,CAAA;AAE/B,SAASC,GAAG,EAAEC,IAAI,QAAQ,uBAAuB,CAAA;AACjD,SAASC,cAAc,QAAQ,sBAAsB,CAAA;AACrD,SAASC,GAAG,QAAQ,sBAAsB,CAAA;AAS1C,OAAO,SAASC,MAAM,CACrB,6DAA6D;AAC7DC,MAAa,GAAGJ,IAAI,EACpBK,MAAqC,EAC7B;IACR,OAAQA,MAAM,CAACC,IAAI;QAClB,KAAKb,KAAK;YACT,MAAK;QACN,KAAKE,UAAU,CAAC;QAChB,KAAKC,UAAU,CAAC;QAChB,KAAKE,aAAa,CAAC;QACnB,KAAKD,aAAa;YACjB,OAAOG,IAAI,CAAA;QACZ,KAAKV,UAAU,CAAC;QAChB,KAAKI,mBAAmB,CAAC;QACzB,KAAKF,QAAQ,CAAC;QACd,KAAKD,IAAI,CAAC;QACV;YACC,OAAOQ,GAAG,CAAA;KACX;IAED,MAAM,EAAEQ,SAAS,EAAG,EAAE,CAAA,EAAEC,aAAa,EAAG,EAAE,CAAA,EAAE,GAAGH,MAAM,CAACI,OAAO;IAC7D,MAAMC,MAAM,GAAGR,GAAG,CAACK,SAAS,EAAEC,aAAa,CAAC;IAC5C,MAAMG,SAAS,GACdD,MAAM,CAACE,MAAM,GAAG,CAAC,IAAI,CAACX,cAAc,CAACM,SAAS,EAAEC,aAAa,CAAC;IAE/D,IAAI,CAACG,SAAS,EAAE;QACf,OAAOX,IAAI,CAAA;KACX;IAED,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMa,qBAAqB,GAAGL,aAAa,CAACA,aAAa,CAACI,MAAM,GAAG,CAAC,CAAC;IACrE,MAAME,iBAAiB,GAAGP,SAAS,CAACA,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC;IACzD,IAAIC,qBAAqB,KAAKC,iBAAiB,EAAE;QAChD,IAAID,qBAAqB,EAAE;YAC1BH,MAAM,CAACK,IAAI,CAACF,qBAAqB,CAAC;SAClC;QACD,IAAIC,iBAAiB,EAAE;YACtBJ,MAAM,CAACK,IAAI,CAACD,iBAAiB,CAAC;SAC9B;KACD;IAED,OAAOJ,MAAM,CAAA;CACb"}