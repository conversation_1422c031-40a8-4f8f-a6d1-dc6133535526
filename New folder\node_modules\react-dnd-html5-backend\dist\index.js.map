{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["import type { BackendFactory, DragDropManager } from 'dnd-core'\n\nimport { HTML5BackendImpl } from './HTML5BackendImpl.js'\nimport type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\nexport { getEmptyImage } from './getEmptyImage.js'\nexport * as NativeTypes from './NativeTypes.js'\nexport type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\n\nexport const HTML5Backend: BackendFactory = function createBackend(\n\tmanager: DragDropManager,\n\tcontext?: HTML5BackendContext,\n\toptions?: HTML5BackendOptions,\n): HTML5BackendImpl {\n\treturn new HTML5BackendImpl(manager, context, options)\n}\n"], "names": ["HTML5BackendImpl", "NativeTypes", "getEmptyImage", "HTML5Backend", "createBackend", "manager", "context", "options"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,uBAAuB,CAAA;YAG5CC,YAAW,MAAM,kBAAkB;AAD/C,SAASC,aAAa,QAAQ,oBAAoB,CAAA;SACtCD,YAAW,IAAXA,WAAW;AAGvB,OAAO,MAAME,YAAY,GAAmB,SAASC,aAAa,CACjEC,OAAwB,EACxBC,OAA6B,EAC7BC,OAA6B,EACV;IACnB,OAAO,IAAIP,gBAAgB,CAACK,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC,CAAA;CACtD,CAAA"}