{"version": 3, "sources": ["../../../src/hooks/useDrag/connectors.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport type { SourceConnector } from '../../internals/index.js'\n\nexport function useConnectDragSource(connector: SourceConnector) {\n\treturn useMemo(() => connector.hooks.dragSource(), [connector])\n}\n\nexport function useConnectDragPreview(connector: SourceConnector) {\n\treturn useMemo(() => connector.hooks.dragPreview(), [connector])\n}\n"], "names": ["useMemo", "useConnectDragSource", "connector", "hooks", "dragSource", "useConnectDragPreview", "dragPreview"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO,CAAA;AAI/B,OAAO,SAASC,oBAAoB,CAACC,SAA0B,EAAE;IAChE,OAAOF,OAAO,CAAC,IAAME,SAAS,CAACC,KAAK,CAACC,UAAU,EAAE;IAAA,EAAE;QAACF,SAAS;KAAC,CAAC,CAAA;CAC/D;AAED,OAAO,SAASG,qBAAqB,CAACH,SAA0B,EAAE;IACjE,OAAOF,OAAO,CAAC,IAAME,SAAS,CAACC,KAAK,CAACG,WAAW,EAAE;IAAA,EAAE;QAACJ,SAAS;KAAC,CAAC,CAAA;CAChE"}