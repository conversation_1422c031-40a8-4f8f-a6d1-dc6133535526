import { lazy } from 'react'

// Admin Pages - Lazy loaded for better performance
export const AdminDashboard = lazy(() => import('../../pages/admin/AdminDashboard'))
export const CourseManagement = lazy(() => import('../../pages/admin/CourseManagement'))
export const CreateCourse = lazy(() => import('../../pages/admin/CreateCourse'))
export const EditCourse = lazy(() => import('../../pages/admin/EditCourse'))
export const AdminPanel = lazy(() => import('../../pages/admin/AdminPanel'))
export const UserManagement = lazy(() => import('../../pages/admin/UserManagement'))
export const CreateUser = lazy(() => import('../../pages/admin/CreateUser'))
export const EditUser = lazy(() => import('../../pages/admin/EditUser'))
export const CategoryManagement = lazy(() => import('../../pages/admin/CategoryManagement'))
export const LevelManagement = lazy(() => import('../../pages/admin/LevelManagement'))
export const LanguageManagement = lazy(() => import('../../pages/admin/LanguageManagement'))
export const ClassManagement = lazy(() => import('../../pages/admin/ClassManagement'))
export const BannedAccounts = lazy(() => import('../../pages/admin/BannedAccounts'))
export const PaymentCodes = lazy(() => import('../../pages/admin/PaymentCodes'))

// Admin Components
export const WatermarkSettings = lazy(() => import('../admin/WatermarkSettings'))
export const HomepageSettings = lazy(() => import('../admin/HomepageSettings'))
export const NotificationSettings = lazy(() => import('../admin/NotificationSettings'))
export const SiteSettings = lazy(() => import('../admin/SiteSettings'))
export const GeneralSettings = lazy(() => import('../admin/GeneralSettings'))

// Instructor Pages
export const InstructorDashboard = lazy(() => import('../../pages/instructor/InstructorDashboard'))
export const MyCourses = lazy(() => import('../../pages/instructor/MyCourses'))
export const CreateSection = lazy(() => import('../../pages/instructor/CreateSection'))
export const CreateLesson = lazy(() => import('../../pages/instructor/CreateLesson'))
export const CreateQuiz = lazy(() => import('../../pages/instructor/CreateQuiz'))
export const StudentResults = lazy(() => import('../../pages/instructor/StudentResults'))

// Student Pages
export const CourseView = lazy(() => import('../../pages/student/CourseView'))
export const LessonView = lazy(() => import('../../pages/student/LessonView'))
export const QuizView = lazy(() => import('../../pages/student/QuizView'))
export const Profile = lazy(() => import('../../pages/student/Profile'))
export const StudentDashboard = lazy(() => import('../../pages/student/StudentDashboard'))
export const Courses = lazy(() => import('../../pages/student/Courses'))

// Public Pages
export const CategoryView = lazy(() => import('../../pages/public/CategoryView'))
export const LevelView = lazy(() => import('../../pages/public/LevelView'))
export const ClassView = lazy(() => import('../../pages/public/ClassView'))
export const RedeemCode = lazy(() => import('../../pages/RedeemCode'))
export const SetupWizard = lazy(() => import('../../pages/SetupWizard'))

// Loading component for Suspense fallback
export const LazyLoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
  </div>
)
