import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faGraduationCap,
  faUsers,
  faClock,
  faSearch,
  faFilter,
  faStar,
  faPlay,
  faCheckCircle,
  faGlobe,
  faUserGroup,
  faBook
} from '@fortawesome/free-solid-svg-icons'
import Header from '../../components/common/Header'
import LoadingSpinner from '../../components/common/LoadingSpinner'
import ImageWithFallback from '../../components/common/ImageWithFallback'
import { useAuth } from '../../context/AuthContext'
import { useCourses } from '../../hooks/useCourses'

const Courses = () => {
  const { user } = useAuth()

  // React Query hook for courses
  const {
    data: courses = [],
    isLoading: loading,
    error: coursesError
  } = useCourses()

  const [filteredCourses, setFilteredCourses] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [levelFilter, setLevelFilter] = useState('all')
  const [languageFilter, setLanguageFilter] = useState('all')
  const [classFilter, setClassFilter] = useState('all')
  const [enrollmentFilter, setEnrollmentFilter] = useState('all')
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    filterCourses()
  }, [courses, searchTerm, categoryFilter, levelFilter, languageFilter, classFilter, enrollmentFilter])

  const filterCourses = () => {
    // Ensure courses is an array before filtering
    if (!Array.isArray(courses)) {
      setFilteredCourses([])
      return
    }

    let filtered = courses

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(course =>
        course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.instructor_name?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(course => course.category_slug === categoryFilter)
    }

    // Level filter
    if (levelFilter !== 'all') {
      filtered = filtered.filter(course => course.level_slug === levelFilter)
    }

    // Language filter
    if (languageFilter !== 'all') {
      filtered = filtered.filter(course => course.language_code === languageFilter)
    }

    // Class filter
    if (classFilter !== 'all') {
      filtered = filtered.filter(course => course.class_slug === classFilter)
    }

    // Enrollment filter
    if (enrollmentFilter !== 'all') {
      const isEnrolled = (course) => course.enrolledStudents?.includes(user.id)
      if (enrollmentFilter === 'enrolled') {
        filtered = filtered.filter(isEnrolled)
      } else if (enrollmentFilter === 'available') {
        filtered = filtered.filter(course => !isEnrolled(course))
      }
    }

    setFilteredCourses(filtered)
  }

  const getUniqueCategories = () => {
    if (!Array.isArray(courses)) return []

    const categories = [...new Set(courses.map(course => ({
      slug: course.category_slug,
      name: course.category_name,
      parent_name: course.parent_name
    })).filter(category => category.slug))]
    return categories.sort((a, b) => {
      // Sort by parent first, then by name
      if (a.parent_name && !b.parent_name) return 1
      if (!a.parent_name && b.parent_name) return -1
      if (a.parent_name !== b.parent_name) return (a.parent_name || '').localeCompare(b.parent_name || '')
      return a.name.localeCompare(b.name)
    })
  }

  const getUniqueLevels = () => {
    if (!Array.isArray(courses)) return []

    const levels = [...new Set(courses.map(course => ({
      slug: course.level_slug,
      name: course.level_name
    })).filter(level => level.slug))]
    return levels
  }

  const getUniqueLanguages = () => {
    if (!Array.isArray(courses)) return []

    const languages = [...new Set(courses.map(course => ({
      code: course.language_code,
      name: course.language_name
    })).filter(lang => lang.code))]
    return languages
  }

  const getUniqueClasses = () => {
    if (!Array.isArray(courses)) return []

    const classes = [...new Set(courses.map(course => ({
      slug: course.class_slug,
      name: course.class_name
    })).filter(cls => cls.slug))]
    return classes
  }

  const isEnrolled = (course) => {
    return course.enrolledStudents?.includes(user.id)
  }

  const toggleFilters = () => {
    setShowFilters(prev => !prev)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  // Show error message if there's an error loading courses
  if (coursesError) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <FontAwesomeIcon
              icon={faGraduationCap}
              className="text-6xl text-gray-400 mb-4"
            />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Error Loading Courses
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Failed to load courses. Please try again.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Reload Page
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-[#080c15]">
      <Header />

      <div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Title */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            All Courses
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Discover and enroll in courses to advance your learning journey
          </p>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 mb-8"
        >
          <div className="flex flex-col gap-4">
            {/* Search and Toggle Row */}
            <div className="flex flex-col sm:flex-row gap-3 items-center">
              {/* Search Input */}
              <div className="relative flex-grow w-full">
                <input
                  type="text"
                  placeholder="Search courses..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <FontAwesomeIcon
                  icon={faSearch}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                />
              </div>

              {/* Filter Toggle Button (Mobile) */}
              <button
                className="sm:hidden flex items-center justify-center px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-gray-600 w-full"
                onClick={toggleFilters}
              >
                <FontAwesomeIcon icon={faFilter} className="w-5 h-5 mr-2 text-blue-600" />
                <span className="text-gray-900 dark:text-white">
                  {showFilters ? 'Hide Filters' : 'Show Filters'}
                </span>
              </button>
            </div>

            {/* Filter Dropdowns */}
            <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 transition-all duration-300 overflow-hidden ${
              showFilters ? 'max-h-96 sm:max-h-none' : 'max-h-0 sm:max-h-none'
            } sm:overflow-visible`}>
              <select
                className="px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white cursor-pointer appearance-none bg-no-repeat bg-right bg-[length:1rem] transition-all hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239ca3af'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E")`,
                  backgroundPosition: 'right 0.75rem center'
                }}
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
              >
                <option value="all">All Categories</option>
                {getUniqueCategories().map(category => (
                  <option key={category.slug} value={category.slug}>
                    {category.parent_name ? `${category.parent_name} → ${category.name}` : category.name}
                  </option>
                ))}
              </select>

              <select
                className="px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white cursor-pointer appearance-none bg-no-repeat bg-right bg-[length:1rem] transition-all hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239ca3af'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E")`,
                  backgroundPosition: 'right 0.75rem center'
                }}
                value={levelFilter}
                onChange={(e) => setLevelFilter(e.target.value)}
              >
                <option value="all">All Levels</option>
                {getUniqueLevels().map(level => (
                  <option key={level.slug} value={level.slug}>
                    {level.name}
                  </option>
                ))}
              </select>

              <select
                className="px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white cursor-pointer appearance-none bg-no-repeat bg-right bg-[length:1rem] transition-all hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239ca3af'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E")`,
                  backgroundPosition: 'right 0.75rem center'
                }}
                value={languageFilter}
                onChange={(e) => setLanguageFilter(e.target.value)}
              >
                <option value="all">All Languages</option>
                {getUniqueLanguages().map(language => (
                  <option key={language.code} value={language.code}>
                    {language.name}
                  </option>
                ))}
              </select>

              <select
                className="px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white cursor-pointer appearance-none bg-no-repeat bg-right bg-[length:1rem] transition-all hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239ca3af'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E")`,
                  backgroundPosition: 'right 0.75rem center'
                }}
                value={classFilter}
                onChange={(e) => setClassFilter(e.target.value)}
              >
                <option value="all">All Classes</option>
                {getUniqueClasses().map(cls => (
                  <option key={cls.slug} value={cls.slug}>
                    {cls.name}
                  </option>
                ))}
              </select>

              <select
                className="px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white cursor-pointer appearance-none bg-no-repeat bg-right bg-[length:1rem] transition-all hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239ca3af'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E")`,
                  backgroundPosition: 'right 0.75rem center'
                }}
                value={enrollmentFilter}
                onChange={(e) => setEnrollmentFilter(e.target.value)}
              >
                <option value="all">All Courses</option>
                <option value="enrolled">My Courses</option>
                <option value="available">Available</option>
              </select>
            </div>
          </div>

          {/* Results Count */}
          <div className="flex justify-end mt-4">
            <span className="text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">
              {filteredCourses.length} course{filteredCourses.length !== 1 ? 's' : ''}
            </span>
          </div>
        </motion.div>

        {/* Courses Grid */}
        {filteredCourses.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredCourses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
              >
                <Link
                  to={`/courses/${course.id}`}
                  className="block bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 active:scale-95 relative group cursor-pointer"
                >
                {/* Pulse Effect */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-white/70 rounded-full opacity-0 scale-0 group-active:opacity-50 group-active:scale-200 transition-all duration-500 z-10 pointer-events-none pulse-effect"></div>

                {/* Course Image */}
                <div className="h-48 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden">
                  <ImageWithFallback
                    src={course.thumbnail_url}
                    alt={course.title}
                    className="w-full h-full object-cover"
                    fallbackIcon={faGraduationCap}
                    fallbackText="Course Image"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
                  {isEnrolled(course) && (
                    <div className="absolute top-3 right-3">
                      <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1">
                        <FontAwesomeIcon icon={faCheckCircle} />
                        <span>Enrolled</span>
                      </span>
                    </div>
                  )}
                </div>

                {/* Course Content */}
                <div className="p-5">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                    {course.title}
                  </h3>

                  <div className="flex items-center mb-3">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faUsers} className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-1" />
                      <span className="text-sm text-gray-500 dark:text-gray-400">{course.enrollment_count || 0}</span>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {course.category_name && (
                      <span className="inline-flex items-center px-3 py-1 text-xs rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
                        <FontAwesomeIcon icon={faBook} className="w-3 h-3 mr-1" />
                        {course.category_name}
                      </span>
                    )}
                    {course.level_name && (
                      <span className={`inline-flex items-center px-3 py-1 text-xs rounded-full ${
                        course.level_slug === 'beginner' ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400' :
                        course.level_slug === 'intermediate' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400' :
                        course.level_slug === 'advanced' ? 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400' :
                        'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400'
                      }`}>
                        {course.level_name}
                      </span>
                    )}
                    {course.language_name && (
                      <span className="inline-flex items-center px-3 py-1 text-xs rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
                        <FontAwesomeIcon icon={faGlobe} className="w-3 h-3 mr-1" />
                        {course.language_name}
                      </span>
                    )}
                    {course.class_name && (
                      <span className="inline-flex items-center px-3 py-1 text-xs rounded-full bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400">
                        <FontAwesomeIcon icon={faUserGroup} className="w-3 h-3 mr-1" />
                        {course.class_name}
                      </span>
                    )}
                  </div>

                  {/* Course Status */}
                  <div className="flex items-center justify-center py-2 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium">
                    <FontAwesomeIcon icon={isEnrolled(course) ? faPlay : faBook} className="w-5 h-5 mr-2" />
                    <span>{isEnrolled(course) ? 'Continue Learning' : 'View Course'}</span>
                  </div>
                </div>

                </Link>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-center py-12"
          >
            <FontAwesomeIcon
              icon={faGraduationCap}
              className="text-6xl text-gray-400 mb-4"
            />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No courses found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {courses.length === 0
                ? "No courses are available yet."
                : "Try adjusting your search or filter criteria."
              }
            </p>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default Courses
