const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { initializePool } = require('./config/database');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      ...helmet.contentSecurityPolicy.getDefaultDirectives(),
      "img-src": ["'self'", "data:", "https:", "http:", "http://localhost:3002"],
    },
  },
  crossOriginResourcePolicy: {
    policy: "cross-origin"
  }
}));
// Smart CORS configuration with auto-detection
const configureCORS = () => {
  return cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (mobile apps, Postman, etc.)
      if (!origin) {
        console.log('🔓 Allowing request with no origin (mobile app/API client)');
        return callback(null, true);
      }

      console.log('🌐 CORS request from origin:', origin);

      // Parse origin URL
      let originHost;
      try {
        const originUrl = new URL(origin);
        originHost = originUrl.hostname;
      } catch (error) {
        console.warn('⚠️ Invalid origin URL:', origin);
        return callback(null, false);
      }

      // Always allow localhost and local IPs for development
      const isLocalhost = originHost === 'localhost' || originHost === '127.0.0.1';
      const isLocalNetwork = originHost.match(/^(192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.)/);

      if (isLocalhost || isLocalNetwork) {
        console.log('✅ Allowing local/development origin:', origin);
        return callback(null, true);
      }

      // In production, be more flexible but log for monitoring
      if (process.env.NODE_ENV === 'production') {
        console.log('🚀 Production mode - allowing origin:', origin);
        return callback(null, true);
      }

      // Development mode - allow common development ports
      const developmentOrigins = [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:5173',
        'http://localhost:5174',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:5173'
      ];

      if (developmentOrigins.includes(origin)) {
        console.log('✅ Allowing development origin:', origin);
        return callback(null, true);
      }

      // Log rejected origins for debugging
      console.warn('❌ Rejecting origin:', origin);
      return callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Origin',
      'X-Requested-With',
      'Accept',
      'Cache-Control',
      'X-Forwarded-For',
      'X-Real-IP'
    ],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
    maxAge: 86400 // 24 hours
  });
};

app.use(configureCORS());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 100 : 1000, // Higher limit for development
  message: {
    error: 'Too many requests, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});
app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/api/setup', require('./routes/setup'));
app.use('/api/auth', require('./routes/auth'));
app.use('/api/courses', require('./routes/courses'));
app.use('/api/users', require('./routes/users'));
app.use('/api/lessons', require('./routes/lessons'));
app.use('/api/quizzes', require('./routes/quizzes'));
app.use('/api/files', require('./routes/files'));
app.use('/api/course-sections', require('./routes/course-sections'));
app.use('/api/settings', require('./routes/settings'));
app.use('/api/categories', require('./routes/categories'));
app.use('/api/course-levels', require('./routes/course-levels'));
app.use('/api/course-languages', require('./routes/course-languages'));
app.use('/api/course-classes', require('./routes/course-classes'));
app.use('/api/payments', require('./routes/payments'));
app.use('/api/payment-codes', require('./routes/payment-codes'));
app.use('/api/notifications', require('./routes/notifications'));
app.use('/api/site-settings', require('./routes/site-settings'));
app.use('/api/cheating-warnings', require('./routes/cheating'));

// Serve uploaded files with proper headers
app.use('/uploads', (req, res, next) => {
  // Set CORS headers for file access
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
}, express.static('uploads'));

// Enhanced health check endpoint with comprehensive system info
app.get('/api/health', async (req, res) => {
  const startTime = Date.now();

  try {
    // Test database connection if available
    let dbStatus = 'not_configured';
    let dbInfo = null;

    try {
      const { getPool } = require('./config/database');
      const pool = getPool();
      const connection = await pool.getConnection();
      await connection.ping();
      connection.release();

      dbStatus = 'connected';
      dbInfo = {
        host: process.env.DB_HOST,
        database: process.env.DB_NAME,
        connectionLimit: pool.config ? pool.config.connectionLimit : 'unknown'
      };
    } catch (dbError) {
      dbStatus = 'error';
      dbInfo = { error: dbError.message };
    }

    const responseTime = Date.now() - startTime;

    const healthInfo = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      server: {
        port: PORT,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version,
        platform: process.platform
      },
      database: {
        status: dbStatus,
        info: dbInfo
      },
      api: {
        responseTime: `${responseTime}ms`,
        cors: 'enabled',
        rateLimit: 'enabled',
        helmet: 'enabled'
      },
      frontend: {
        configuredUrl: process.env.FRONTEND_URL || 'auto-detect',
        allowedOrigins: process.env.NODE_ENV === 'production' ? 'all' : 'development_only'
      }
    };

    // Add request info for debugging
    if (process.env.NODE_ENV === 'development') {
      healthInfo.request = {
        origin: req.get('Origin'),
        userAgent: req.get('User-Agent'),
        ip: req.ip || req.connection.remoteAddress,
        headers: req.headers
      };
    }

    res.json(healthInfo);
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message,
      version: '1.0.0'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Route not found' });
});

// Initialize database pool if configuration exists
initializePool();

app.listen(PORT, () => {
  console.log('\n🚀 Educational Platform Backend Server Started');
  console.log('='.repeat(50));
  console.log(`📅 Started at: ${new Date().toISOString()}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔌 Port: ${PORT}`);
  console.log(`📦 Node.js: ${process.version}`);
  console.log(`💾 Platform: ${process.platform}`);

  console.log('\n🔗 API Endpoints:');
  console.log(`   Health Check: http://localhost:${PORT}/api/health`);
  console.log(`   API Base URL: http://localhost:${PORT}/api`);

  console.log('\n🌐 Network Access:');
  console.log(`   Local: http://localhost:${PORT}`);
  console.log(`   Local: http://127.0.0.1:${PORT}`);

  // Try to detect local network IP
  const os = require('os');
  const networkInterfaces = os.networkInterfaces();
  const localIPs = [];

  Object.keys(networkInterfaces).forEach(interfaceName => {
    networkInterfaces[interfaceName].forEach(networkInterface => {
      if (networkInterface.family === 'IPv4' && !networkInterface.internal) {
        localIPs.push(networkInterface.address);
      }
    });
  });

  if (localIPs.length > 0) {
    console.log('   Network IPs:');
    localIPs.forEach(ip => {
      console.log(`     http://${ip}:${PORT}`);
    });
  }

  console.log('\n📱 Frontend Configuration:');
  if (process.env.FRONTEND_URL && process.env.FRONTEND_URL !== 'auto') {
    console.log(`   Configured URL: ${process.env.FRONTEND_URL}`);
  } else {
    console.log('   URL Detection: Auto (will detect from request origin)');
  }

  console.log('\n🔒 Security Features:');
  console.log('   ✅ CORS: Enabled with smart origin detection');
  console.log('   ✅ Helmet: Enabled for security headers');
  console.log('   ✅ Rate Limiting: Enabled');
  console.log('   ✅ Request Logging: Enabled');

  console.log('\n💾 Database:');
  if (process.env.DB_HOST) {
    console.log(`   Host: ${process.env.DB_HOST}:${process.env.DB_PORT || 3306}`);
    console.log(`   Database: ${process.env.DB_NAME}`);
    console.log('   Status: Will be tested on first request');
  } else {
    console.log('   Status: Not configured (setup required)');
  }

  console.log('\n' + '='.repeat(50));
  console.log('✅ Server is ready to accept connections!');
  console.log('💡 Visit the health check endpoint to verify everything is working');
  console.log('');
});
