{"version": 3, "sources": ["../../src/reducers/index.ts"], "sourcesContent": ["import type { Action } from '../interfaces.js'\nimport { get } from '../utils/js_utils.js'\nimport type { State as DirtyHandlerIdsState } from './dirtyHandlerIds.js'\nimport { reduce as dirtyHandlerIds } from './dirtyHandlerIds.js'\nimport type { State as DragOffsetState } from './dragOffset.js'\nimport { reduce as dragOffset } from './dragOffset.js'\nimport type { State as DragOperationState } from './dragOperation.js'\nimport { reduce as dragOperation } from './dragOperation.js'\nimport type { State as RefCountState } from './refCount.js'\nimport { reduce as refCount } from './refCount.js'\nimport type { State as StateIdState } from './stateId.js'\nimport { reduce as stateId } from './stateId.js'\n\nexport interface State {\n\tdirtyHandlerIds: DirtyHandlerIdsState\n\tdragOffset: DragOffsetState\n\trefCount: RefCountState\n\tdragOperation: DragOperationState\n\tstateId: StateIdState\n}\n\nexport function reduce(state: State = {} as State, action: Action<any>): State {\n\treturn {\n\t\tdirtyHandlerIds: dirtyHandlerIds(state.dirtyHandlerIds, {\n\t\t\ttype: action.type,\n\t\t\tpayload: {\n\t\t\t\t...action.payload,\n\t\t\t\tprevTargetIds: get<string[]>(state, 'dragOperation.targetIds', []),\n\t\t\t},\n\t\t}),\n\t\tdragOffset: dragOffset(state.dragOffset, action),\n\t\trefCount: refCount(state.refCount, action),\n\t\tdragOperation: dragOperation(state.dragOperation, action),\n\t\tstateId: stateId(state.stateId),\n\t}\n}\n"], "names": ["get", "reduce", "dirtyHandlerIds", "dragOffset", "dragOperation", "refCount", "stateId", "state", "action", "type", "payload", "prevTargetIds"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAASA,GAAG,QAAQ,sBAAsB,CAAA;AAE1C,SAASC,MAAM,IAAIC,eAAe,QAAQ,sBAAsB,CAAA;AAEhE,SAASD,MAAM,IAAIE,UAAU,QAAQ,iBAAiB,CAAA;AAEtD,SAASF,MAAM,IAAIG,aAAa,QAAQ,oBAAoB,CAAA;AAE5D,SAASH,MAAM,IAAII,QAAQ,QAAQ,eAAe,CAAA;AAElD,SAASJ,MAAM,IAAIK,OAAO,QAAQ,cAAc,CAAA;AAUhD,OAAO,SAASL,MAAM,CAACM,KAAY,GAAG,EAAE,AAAS,EAAEC,MAAmB,EAAS;IAC9E,OAAO;QACNN,eAAe,EAAEA,eAAe,CAACK,KAAK,CAACL,eAAe,EAAE;YACvDO,IAAI,EAAED,MAAM,CAACC,IAAI;YACjBC,OAAO,EAAE,kBACLF,MAAM,CAACE,OAAO;gBACjBC,aAAa,EAAEX,GAAG,CAAWO,KAAK,EAAE,yBAAyB,EAAE,EAAE,CAAC;cAClE;SACD,CAAC;QACFJ,UAAU,EAAEA,UAAU,CAACI,KAAK,CAACJ,UAAU,EAAEK,MAAM,CAAC;QAChDH,QAAQ,EAAEA,QAAQ,CAACE,KAAK,CAACF,QAAQ,EAAEG,MAAM,CAAC;QAC1CJ,aAAa,EAAEA,aAAa,CAACG,KAAK,CAACH,aAAa,EAAEI,MAAM,CAAC;QACzDF,OAAO,EAAEA,OAAO,CAACC,KAAK,CAACD,OAAO,CAAC;KAC/B,CAAA;CACD"}