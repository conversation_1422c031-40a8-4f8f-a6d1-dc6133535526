import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { authService } from '../services/api'
import { queryKeys } from '../config/reactQuery'
import toast from 'react-hot-toast'

// Get current user
export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.auth.user,
    queryFn: () => {
      const user = authService.getCurrentUser()
      if (!user) {
        throw new Error('No user found')
      }
      return user
    },
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!localStorage.getItem('token'),
  })
}

// Verify token
export const useVerifyToken = (token) => {
  return useQuery({
    queryKey: queryKeys.auth.verify(token),
    queryFn: () => authService.verifyToken(token),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!token,
  })
}

// Login mutation
export const useLogin = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: authService.login,
    onSuccess: (data) => {
      // Store token and user data
      localStorage.setItem('token', data.token)
      localStorage.setItem('user', JSON.stringify(data.user))
      
      // Update cache
      queryClient.setQueryData(queryKeys.auth.user, data.user)
      
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: ['users'] })
      
      toast.success('Login successful!')
    },
    onError: (error) => {
      console.error('Login error:', error)
      toast.error(error.message || 'Login failed')
    },
  })
}

// Register mutation
export const useRegister = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: authService.register,
    onSuccess: (data) => {
      // Store token and user data
      localStorage.setItem('token', data.token)
      localStorage.setItem('user', JSON.stringify(data.user))
      
      // Update cache
      queryClient.setQueryData(queryKeys.auth.user, data.user)
      
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: ['users'] })
      
      toast.success('Registration successful!')
    },
    onError: (error) => {
      console.error('Registration error:', error)
      toast.error(error.message || 'Registration failed')
    },
  })
}

// Forgot password mutation
export const useForgotPassword = () => {
  return useMutation({
    mutationFn: authService.forgotPassword,
    onSuccess: () => {
      toast.success('Password reset email sent!')
    },
    onError: (error) => {
      console.error('Forgot password error:', error)
      toast.error(error.message || 'Failed to send reset email')
    },
  })
}

// Reset password mutation
export const useResetPassword = () => {
  return useMutation({
    mutationFn: ({ token, password }) => authService.resetPassword(token, password),
    onSuccess: () => {
      toast.success('Password reset successful!')
    },
    onError: (error) => {
      console.error('Reset password error:', error)
      toast.error(error.message || 'Password reset failed')
    },
  })
}

// Logout function (not a mutation since it's local)
export const useLogout = () => {
  const queryClient = useQueryClient()
  
  return () => {
    // Clear local storage
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    // Clear all cached data
    queryClient.clear()
    
    // Redirect to login
    window.location.href = '/login'
    
    toast.success('Logged out successfully')
  }
}
